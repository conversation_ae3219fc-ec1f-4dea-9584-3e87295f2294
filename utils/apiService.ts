// import axios from 'axios';

// // Create axios instance
// const api = axios.create({
//   baseURL: process.env.NEXT_PUBLIC_API_BASE_URL, // Use the NEXT_PUBLIC_ prefix for public environment variables
// });

// // Optional: Global interceptors for request and response handling
// api.interceptors.request.use(
//   (config) => {
//     // Attach token from cookies or other storage mechanism (not localStorage on the server-side)
//     const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
    
//     // If the token exists and it's client-side, attach it to the request headers
//     if (token) {
//       config.headers.Authorization = `Bearer ${token}`;
//     }

//     return config;
//   },
//   (error) => Promise.reject(error)
// );

// api.interceptors.response.use(
//   (response) => response,
//   (error) => {
//     // Handle errors globally, e.g., redirect on 401 (Unauthorized)
//     if (error.response?.status === 401) {
//       // You can add logic to redirect to login here
//       // e.g., Router.push('/login');
//     }
//     return Promise.reject(error);
//   }
// );

// /**
//  * Generic API call function
//  *
//  * @param {string} method - The HTTP method (GET, POST, PUT, DELETE, etc.)
//  * @param {string} url - The URL of the API endpoint
//  * @param {object} [data] - Request body for POST/PUT requests
//  * @param {object} [headers] - Optional custom headers
//  * @param {object} [params] - Optional query parameters for GET requests
//  */
// export const callApi = async ({ method, url, data = {}, headers = {}, params = {} }) => {
//   try {
//     const response = await api({
//       method,
//       url,
//       data,
//       headers,
//       params,
//     });
//     return response.data;
//   } catch (error) {
//     console.error("API Error: ", error);  // Log the error for debugging
//     throw error;
//   }
// };

import axios from 'axios';

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
});

interface CallApiArgs {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: object;
  headers?: object;
  params?: object;
}

export const callApi = async ({ method, url, data = {}, headers = {}, params = {} }: CallApiArgs) => {
  try {
    const response = await api({
      method,
      url,
      data,
      headers,
      params,
    });
    return response.data;
  } catch (error) {
    console.error("API Error:", error);
    throw error;
  }
};

