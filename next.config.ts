// import type { NextConfig } from 'next';
import { NextConfig as ActualNextConfig } from 'next';

const nextConfig: ActualNextConfig = {
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],
 typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // async redirects() {
  //   return [
  //     {
  //       source: '/:city/Bars',
  //       destination: '/:city/bars',
  //       permanent: true,
  //     },
  //   ];
  // },
  
 


};

export default nextConfig;
