// import type { NextConfig } from 'next';
import { NextConfig as ActualNextConfig } from 'next';

const nextConfig: ActualNextConfig = {
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Optimize CSS loading to prevent FOUC
  experimental: {
    optimizeCss: true,
  },
  // Ensure proper CSS bundling
  webpack: (config, { dev, isServer }) => {
    // Optimize CSS loading in production
    if (!dev && !isServer) {
      config.optimization.splitChunks.cacheGroups.styles = {
        name: 'styles',
        test: /\.(css|scss|sass)$/,
        chunks: 'all',
        enforce: true,
      };
    }
    return config;
  },
  // async redirects() {
  //   return [
  //     {
  //       source: '/:city/Bars',
  //       destination: '/:city/bars',
  //       permanent: true,
  //     },
  //   ];
  // },
};

export default nextConfig;
