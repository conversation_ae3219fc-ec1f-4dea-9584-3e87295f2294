import { useState, useCallback } from 'react';
import { callApi } from '../../utils/apiService'; // adjust the import path as necessary

type ApiRequestParams = {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: object;
  params?: object;
  headers?: object;
};

const useApi = () => {
  const [data, setData] = useState<unknown | null>(null); // More precise typing
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const request = useCallback(async ({ method, url, data = {}, params = {}, headers = {} }: ApiRequestParams) => {
    setLoading(true);
    setError(null);

    try {
      const response = await callApi({ method, url, data, params, headers });
      setData(response);
      return response;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Something went wrong');
      } else {
        setError('Something went wrong');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { data, error, loading, request };
};

export default useApi;
