import React from "react";

const PreloadPopulars = () => {

  return (
    <section className="mt-10 " id="popular-preload">
      <div className="px-4 py-20 animate-pulse ">
        {/* <!--title--> */}
        <div className="text-center ">
          {/* <p className="text-lg text-[#F77C3E] font-semibold">Lorem Ipsum is simply</p> */}
          <h1 className="w-60 mx-auto h-10 rounded bg-gray-100 ">
          </h1>
        </div>
        {/* <!--grid--> */}
        <div className="max-w-6xl mx-auto ">
          <div class="relative grid xl:grid-cols-3 gap-4 mt-10">
            <div>
              <div className="rounded-2xl overflow-hidden grid sm:grid-cols-2 
            lg:grid-cols-1 xl:grid-cols-1 gap-4   cursor-pointer">
                <div className="grid gap-4">
                  <div className="lg:flex bg-gray-100 rounded-xl overflow-hidden">
                    <div class="lg:w-5/12 h-20 bg-gray-50"></div>
                  </div>
                  <div className="lg:flex bg-gray-100 rounded-xl overflow-hidden">
                    <div class="lg:w-5/12 h-20 bg-gray-50"></div>
                  </div>
                  <div className="lg:flex bg-gray-100 rounded-xl overflow-hidden">
                    <div class="lg:w-5/12 h-20 bg-gray-50"></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="lg:flex bg-gray-100 rounded-xl overflow-hidden">
              <div class="w-full h-40 bg-gray-50"></div>
            </div>
            <div>
              <div className="rounded-2xl overflow-hidden grid sm:grid-cols-2 
            lg:grid-cols-1 xl:grid-cols-1 gap-4   cursor-pointer">
                <div className="grid gap-4">
                  <div className="lg:flex bg-gray-100 rounded-xl overflow-hidden">
                    <div class="lg:w-5/12 h-20 bg-gray-50"></div>
                  </div>
                  <div className="lg:flex bg-gray-100 rounded-xl overflow-hidden">
                    <div class="lg:w-5/12 h-20 bg-gray-50"></div>
                  </div>
                  <div className="lg:flex bg-gray-100 rounded-xl overflow-hidden">
                    <div class="lg:w-5/12 h-20 bg-gray-50"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PreloadPopulars;
