import { useEffect, useState } from "react";

const cities = [
  {
    name: "Paris",
    image:
      "../../assets/Paris.jpg",
  },
  {
    name: "Marseille",
    image:
      "../../assets/Marseille.jpg",
  },
  {
    name: "Montpellier",
    image:
      "../../assets/Montpellier.jpg",
  },
  {
    name: "Nice",
    image:
      "../../assets/Nice.jpg",
  },
];

const CitySelectorModal = () => {
  const [isOpen, setIsOpen] = useState(true);
  const [selectedCity, setSelectedCity] = useState(null);

  const selectCity = (city) => {
    setSelectedCity(city);
    //console.log("city---------",city)
    localStorage.setItem("selectedCity", JSON.stringify(city));
    localStorage.setItem("city", city.name);
    setIsOpen(false);
    window.location.reload()
  };

  const closeModal = () => setIsOpen(false);

  useEffect(() => {
    const storedCity = localStorage.getItem("selectedCity");
    if (storedCity) {
      setSelectedCity(JSON.parse(storedCity));
      setIsOpen(false);
    }
  }, []);

  return (
    <>
      {isOpen && (
        // <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div className="fixed inset-0 bg-gradient-to-r from-[#F3C160] to-[#F77C3E] bg-opacity-80 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-xl w-11/12 md:w-1/2 lg:w-1/3 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-2 border-b">
              <div className="flex justify-center items-center p-2  w-full">
                <div className="text-center">
                  <p className="text-xl font-semibold text-gray-800 leading-relaxed text-center">
                    Welcome to <span className="text-[#F3C160]">Seekes</span>!
                  </p>
                  <p className="text-xl font-semibold text-[#F3C160] leading-relaxed text-center">
                    {/* Let’s start by picking your city. */}
                    Let’s start by picking your city. Bars and clubs are waiting — let’s enjoy!
                  </p>
                </div>
              </div>
              {/* <h3 className="text-xl font-semibold text-gray-800"> */}
              {/* <p className="text-xl font-semibold text-gray-800 text-center leading-relaxed">
  Welcome to <span className="text-[#F3C160]">Seekes</span>! <br />
 <span className="text-[#F3C160] text-center"> Let’s start by picking your city.</span>
</p> */}

              {/* </h3> */}
              {/* <button onClick={closeModal} className="text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button> */}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4">
              {cities.map((city) => (
                <div key={city.name} className="group cursor-pointer" onClick={() => selectCity(city)}>
                  <div className="relative overflow-hidden rounded-lg h-40">
                    <img
                      src={city.image}
                      alt={city.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gray bg-opacity-30 flex items-end p-4">
                      <h4 className="text-white text-lg font-bold">{city.name}</h4>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {/* <div className="p-4 border-t flex justify-end">
              <button onClick={closeModal} className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md text-gray-700">
                Cancel
              </button>
            </div> */}
          </div>
        </div>
      )}

      {/* {selectedCity && (
        <div className="fixed bottom-4 left-4 bg-white p-4 rounded-lg shadow-lg z-40">
          <div className="flex items-center space-x-3">
            <img src={selectedCity.image} alt={selectedCity.name} className="w-12 h-12 rounded-full object-cover" />
            <span className="font-medium">{selectedCity.name}</span>
          </div>
        </div>
      )}*/}
    </>
  );
};

export default CitySelectorModal;
