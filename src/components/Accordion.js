import React, { useState } from "react";

const Accordion = ({ type }) => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleAccordion = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  // Decide faqItems based on type
  let faqItems = [];

  if (type === "bars") {
    faqItems = [
      {
        question: "What are the best affordable bars in Paris for students and young travelers?",
        answer: "Paris has a huge range of affordable bars, especially around student-friendly neighborhoods like the Latin Quarter and Oberkampf. Many bars offer happy hour deals on cocktails, beers, and wines, making it easy to enjoy Parisian nightlife without breaking the bank."
      },
      {
        question: "How can find happy hours in Paris and save on drinks?",
        answer: "Many bars across Paris offer generous happy hours, typically between 5 PM and 8 PM. You can find up-to-date happy hour listings on our website and filter by area, drink specials, or type of bar. With happy hours, you often pay half the regular price for beers, cocktails, and wine!"
      },
      {
        question: "What types of bars can find in Paris?",
        answer: "Paris offers everything from cozy wine bars and lively cocktail bars to rooftop lounges, karaoke bars, speakeasies, and themed student bars. Whether you're looking for craft cocktails, a relaxed pub atmosphere, or a late-night singalong, there's a Parisian bar for every taste."
      },
      {
        question: "Where can find unique and hidden bars in Paris?",
        answer: "For something off the beaten path, Paris is famous for its hidden speakeasies and concept bars tucked behind secret doors, laundromats, and even pizza shops. We list the best hidden bars where you can enjoy unique cocktails and an unforgettable atmosphere."
      },
      {
        question: "Are there karaoke bars in Paris and where can find them?",
        answer: "Absolutely! Paris has a lively karaoke scene, from private karaoke rooms to vibrant open-mic nights. Our listings highlight the best karaoke bars where you can sing your heart out with friends."
      },
      {
        question: "Which areas in Paris are best for bar hopping?",
        answer: "The Marais, Bastille, Oberkampf, Pigalle, and Saint-Germain are top neighborhoods for bar hopping, offering a mix of traditional Parisian cafés, cocktail lounges, and student-friendly pubs all within walking distance."
      },
    ];
  } else {
    // Default faqItems if type !== "bars"
    faqItems = [
      {
        question: "How much does it cost to enter clubs in Paris?",
        answer: "Entry fees for Paris clubs vary depending on the venue and the night. Many student-friendly clubs offer free or discounted entry before midnight, while popular nightclubs can charge between €10 and €25. Some high-end clubs require a minimum spend at the door, especially on weekends."
      },
      {
        question: "Can book a VIP table at clubs in Paris and how much does it cost?",
        answer: "Yes, most major clubs in Paris offer VIP table bookings. Prices typically start around €150-€300 for small groups and can go higher for premium locations or larger bottle packages. Booking a table usually includes fast-track entry, reserved seating, and bottle service."
      },
      {
        question: "What is the dress code for Paris nightclubs?",
        answer: "Dress codes in Paris clubs are generally smart and stylish. Upscale clubs may require dress shoes, collared shirts, and elegant attire, while more casual venues are more flexible. Sportswear, flip-flops, and overly casual outfits can often lead to refused entry, so it's best to dress chic."
      },
      {
        question: "What types of music canl expect in Paris clubs?",
        answer: "Paris clubs feature a wide range of music styles, including house, techno, hip-hop, R&B, pop, reggaeton, and Afrobeat. Some clubs specialize in a specific genre, while others offer mixed playlists depending on the night and guest DJs."
      },
      {
        question: "What are the best neighborhoods in Paris for clubbing?",
        answer: "Top areas for nightlife include the Champs-Élysées (for luxury clubs), Oberkampf (for alternative and student clubs), Bastille (for lively dance floors), and Pigalle (for a mix of trendy and underground venues). "
      }
    ];
  }

  return (
    <section>
      <div className="container mx-auto px-4 py-10 lg:py-20 mt-20">
        {/* Title */}
        <div className="text-center">
          <h1 className="text-xl lg:text-4xl font-semibold text-[#262626]">
            Frequently Asked Questions
          </h1>
        </div>

        {/* Accordion */}
        <div className="w-full space-y-2 mt-10">
          {faqItems.map((item, index) => (
            <div key={index} className="rounded-lg overflow-hidden">
              {/* Accordion Header */}
              <button
                className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
                onClick={() => toggleAccordion(index)}
              >
                <span className="font-medium text-[#658079]">{item.question}</span>

                {/* Icon Rotation */}
                <svg
                  id={`icon-${index}`}
                  className={`w-5 h-5 text-[#F77C3E] transition-transform duration-300 ${openIndex === index ? "rotate-45" : ""}`}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>

              {/* Accordion Content */}
              <div
                id={`content-${index}`}
                className={`bg-white px-4 py-2 transition-all duration-500 ease-in-out overflow-hidden ${openIndex === index ? "max-h-40 opacity-100" : "max-h-0 opacity-0"
                  }`}
              >
                {item.answer}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Accordion;
