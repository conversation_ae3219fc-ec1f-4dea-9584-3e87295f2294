// components/Navbar.js
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import Image from "next/image";

const Navbar = () => {
  const router = useRouter();
  const pathname = router.pathname;
  //const city = process.env.NEXT_PUBLIC_CITY;

  const cities = [
    { name: "Paris", image: "../../assets/Paris.jpg" },
    { name: "Marseille", image: "../../assets/Marseille.jpg" },
    { name: "Montpellier", image: "../../assets/Montpellier.jpg" },
    { name: "Nice", image: "../../assets/Nice.jpg" },

  ];

  const [city, setCity] = useState(0);
  const [selectedCity, setSelectedCity] = useState(cities[0]);
  const [hydrated, setHydrated] = useState(false); // track hydration state


  const menuItems = [
    { name: "Bars", path: `/${city}/bars` },
    { name: "Clubs", path: `/${city}/clubs` },
    { name: "Events", path: `/${city}/events` },
    { name: "Blogs", path: `/${city}/blogs` },
    { name: "Contact Us", path: `/${city}/contactus` },
  ];

  useEffect(() => {
    const storedCityName = localStorage.getItem("city");
    if (storedCityName) {
      const matchedCity = cities.find(
        (c) => c.name.toLowerCase() === storedCityName.toLowerCase()
      );
      if (matchedCity) {
        setSelectedCity(matchedCity);
        setCity(matchedCity.name.toLowerCase());
      } else {
        setSelectedCity(cities[0]);
        setCity(cities[0].name.toLowerCase());
      }
    } else {
      setSelectedCity(cities[0]);
      setCity(cities[0].name.toLowerCase());
    }
    setHydrated(true);
  }, []);


  const handleChange = (event) => {
    const selectedOption = event.target.options[event.target.selectedIndex];
    const newCityName = selectedOption.value; // using value instead of id

    // Find full city object
    const selectedCityObj = cities.find(
      (city) => city.name.toLowerCase() === newCityName.toLowerCase()
    );

    if (!selectedCityObj) return;

    // Store full object in localStorage
    localStorage.setItem("city", newCityName.toLowerCase());
    localStorage.setItem("selectedCity", JSON.stringify(selectedCityObj));

    // Update state
    setCity(newCityName.toLowerCase());
    setSelectedCity(selectedCityObj);

    // Update path
    const currentPath = router.asPath;
    const updatedPath = currentPath.replace(/^\/[^\/]+/, `/${newCityName.toLowerCase()}`);

    router.push(updatedPath).then(() => {
      window.location.reload();
    });
  };


  // Until hydration is done, render null or a placeholder to avoid hydration mismatch
  if (!hydrated) {
    return null;
  }

  return (
    <header className="bg-[#fff] shadow-md">
      <nav className="container mx-auto p-4 lg:flex lg:items-center lg:justify-between relative">
        {/* Logo Section */}
        <div className="flex items-center justify-between border-b pb-4 border-gray-200 lg:border-white lg:pb-0 cursor-pointer">
          <Image
            className="h-10"
            src="/assets/logo.svg"
            alt="Logo"
            width={120}
            height={40}
            onClick={() => router.push("/")}
          />
          <button id="menu-btn-mob" className="p-2 lg:hidden">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="size-8"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
              />
            </svg>
          </button>
        </div>

        {/* Navigation Links */}
        <nav id="menu-mob" className="lg:block mt-4 lg:mt-0 hidden lg:blocklg:block mt-4 lg:mt-0">
          <ul className="lg:flex lg:items-center space-y-2 lg:space-y-0 lg:space-x-4">
            {menuItems.map((item) => {
              const isActive = pathname.startsWith(item.path);

              return (
                <li key={item.path}>
                  <Link href={item.path}>
                    <span
                      className={`text-lg font-medium block p-2 rounded cursor-pointer ${isActive ? "text-[#F77C3E] font-bold" : "text-[#262626]"
                        }`}
                    >
                      {item.name}
                    </span>
                  </Link>
                </li>
              );
            })}

            {/* Download App Button */}
            <li className="cursor-pointer">
              <a
                href="#"
                className="text-ms font-medium block px-6 py-3 text-white uppercase bg-gradient-to-r from-[#F3C160] to-[#F77C3E] rounded-full text-center"
              >
                Download App
              </a>
            </li>

            {/* Language Selector */}
            <li className="flex items-center cursor-pointer">
              <span>
                <img src="../../assets/icons/location.svg" className="max-w-full h-[25px]" alt="Globe Icon" />
              </span>
              {/* <select className="text-lg font-base block p-2 text-[#262626] outline-0 focus:outline-0"
                value={city}
                onChange={handleChange}>
                <option id="Paris">Paris</option>
                <option id="Marseille">Marseille</option>
                <option id="Montpellier">Montpellier</option>
                <option id="Nice">Nice</option>
                <option id="Aix-En-Provence">Aix-En-Provence</option>
              </select> */}
              <select
                className="text-lg font-base block p-2 text-[#262626] outline-0 focus:outline-0 border border-gray-300 rounded-md mx-auto"
                value={selectedCity.name}
                onChange={handleChange}
              >
                {cities.map((city) => (
                  <option key={city.name} value={city.name}>
                    {city.name}
                  </option>
                ))}
              </select>
            </li>



          </ul>
        </nav>
      </nav>

      <>
        {selectedCity && (
          <div className="fixed bottom-4 left-4 bg-white p-4 rounded-lg shadow-lg z-40">
            <div className="flex items-center space-x-3">
              <img src={selectedCity.image} alt={selectedCity.name} className="w-12 h-12 rounded-full object-cover" />
              <span className="font-medium">{selectedCity.name}</span>
            </div>
          </div>
        )}

      </>
    </header>
  );
};

export default Navbar;
