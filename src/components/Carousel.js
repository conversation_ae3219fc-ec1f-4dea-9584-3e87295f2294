
import React from "react";
import { useRouter } from 'next/router';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/swiper-bundle.css';
import { formatName } from "../hooks/custom"
// import '../css/styles.css';


const Carousel = ({ data, type, size }) => {
  const itemsToShow = size ? data.slice(0, 4) : data;
  const router = useRouter();
  //const [selectedBar, setSelectedBar] = useState("");
  //const city = process.env.NEXT_PUBLIC_CITY;
  // set the Bar id in local storage
  const setBarIdInLocalStorage = (id, name) => {
    localStorage.setItem("selectedBar", id);
    const cityFromLocalStorage = localStorage.getItem("city");
    //setCity(cityFromLocalStorage);
    const city = cityFromLocalStorage;
    //setSelectedBar(id);
    window.dispatchEvent(new Event("storage"));
    router.push(`/${city}/bar/${name ? formatName(name) : ""}`);
  };

  const setClubTagInLocalStorage = (id, name) => {
    localStorage.setItem("selectedClubId", id);
    const cityFromLocalStorage = localStorage.getItem("city");
    const city = cityFromLocalStorage;
    //router.push("/ClubDetails");
    router.push(`/${city}/club/${name ? formatName(name) : ""}`);
  };


  // Add the Rating 
  const renderRatingStars = (rating) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const stars = [];



    //Update the bar id in localstorage
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <img
          key={`full-${i}`}
          className="w-4 h-4"
          src="../../assets/icons/rating.svg"
          alt="rating"
        />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <img
          key="half"
          className="w-4 h-4"
          src="../../assets/icons/half-rating.svg"
          alt="half-rating"
        />
      );
    }

    return stars;
  };


  if (type === "bars") {

    return (
      <div className="max-w-6xl mx-auto mt-10">
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          slidesPerView={1}
          spaceBetween={20}
          loop={false}
          navigation
          pagination={{ clickable: true }}
          // autoplay={{ delay: 3000 }}
          breakpoints={{
            768: { slidesPerView: 2 },
            1280: { slidesPerView: 4 },
          }}
        >
          {itemsToShow.map((bar, index) => (
            <SwiperSlide key={index}>
              <div className="rounded-2xl bg-gradient-to-r from-[#F3C160] to-[#F77C3E]"

                onClick={() => setBarIdInLocalStorage(bar?.id, bar?.name)}>
                <div className="relative">
                  {bar?.rating % 1 !== 0 && (
                    <div className="flex w-full items-center justify-between absolute p-4">
                      <div className="flex items-center space-x-1 bg-white px-2 pt-1 pb-1.5 rounded-full">
                        {[...Array(Math.floor(bar?.rating))].map((_, i) => (
                          <img
                            key={i}
                            className="w-4 h-4"
                            src="../assets/icons/rating.svg"
                            alt="rating"
                          />
                        ))}
                        {bar?.rating % 1 !== 0 && (
                          <img
                            className="w-4 h-4"
                            src="../assets/icons/half-rating.svg"
                            alt="half rating"
                          />
                        )}
                        <p className="text-xs mt-0.5">({bar?.rating})</p>
                      </div>
                      {/* <div>
                      <img
                        className="w-6 h-6"
                        src="assets/icons/heart.svg"
                        alt="heart"
                      />
                    </div> */}
                    </div>)}

                  <div className="flex w-full items-end justify-between absolute p-4 -bottom-14">
                    {bar.logo == "dsfsfsfdss" ?
                      <div className="w-24 rounded border-2 border-[#EBDABC] overflow-hidden">
                        <img src={bar.logo} alt={bar.name}
                          className="w-full h-16 object-cover overflow-hidden rounded-t-2xl"
                          onError={(e) => (e.target.src = "../assets/popular-logo.png")} />
                      </div> :
                      <div className="w-24 rounded border-2 bg-gradient-to-r from-[#F3C160] to-[#F77C3E] text-black bg-[0E2A46] h-16 border-[#EBDABC] flex items-center justify-center">
                        <h2 className="text-4xl font-bold">{bar.name?.charAt(0)}</h2>
                      </div>
                    }
                    <div className="text-[#262626] flex items-center space-x-2">
                      <img
                        className="w-5 h-5"
                        src="../../assets/icons/mug.svg"
                        alt="mug"
                      />
                      <h3 className="text-lg font-medium">
                        &euro;{bar.price_start_from}
                      </h3>
                    </div>
                  </div>

                  <img
                    src={bar?.images?.[0]?.image}
                    className="w-full h-36  overflow-hidden object-cover rounded-t-xl"
                    alt={bar.name}
                    loading="lazy"
                  />
                </div>

                <div className="px-4 pb-6 pt-14">
                  <h4 className="text-md text-white font-medium">
                    <span>{bar.name}</span>
                  </h4>

                  <div className="mt-2 flex items-center space-x-2">
                    <div className="w-7 h-7 flex-none bg-white rounded-full">
                      <img
                        className="w-full p-2"
                        src="../../assets/icons/location.svg"
                        alt="location"
                      />
                    </div>
                    <p className="text-sm whitespace-nowrap overflow-hidden text-ellipsis">{bar.address}</p>
                  </div>
                  <div className="mt-2 flex space-x-2">
                    <div className="w-7 h-7 bg-white flex-none rounded-full"><img className="w-full p-2" alt=""
                      src="../../assets/icons/mugs.svg" /></div>
                    {bar.tags?.length > 0 && (
                      <div className="flex items-center gap-2 overflow-hidden">
                        {bar.tags?.slice(0, 3).map((tag, idx) => (
                          <span
                            key={idx}
                            className="text-xs px-2 rounded-md py-1 bg-white whitespace-nowrap"
                          >
                            {tag.name}
                          </span>
                        ))}

                        {bar.tags.length > 3 && (
                          <span className="text-xs px-2 rounded-md py-1 bg-white whitespace-nowrap">...</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    );
  }
  else {
    return (
      <div className="max-w-6xl mx-auto mt-10">
        <div className="relative grid md:grid-cols-2 xl:grid-cols-4 gap-4 mt-10 owl-carousel owl-theme"></div>
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          slidesPerView={1}
          spaceBetween={20}
          loop={false}
          navigation
          pagination={{ clickable: true }}
          // autoplay={{ delay: 3000 }}
          breakpoints={{
            768: { slidesPerView: 2 },
            1280: { slidesPerView: 4 },
          }}
        >
          {data.map((item, index) => (
            <SwiperSlide key={index}>
              <div className="item rounded-2xl bg-linear-to-r from-[#262626] to-[#1D4B65]"
                onClick={() => setClubTagInLocalStorage(item?.id, item?.name)}>

                <div className="relative">
                  <div className="flex w-full items-center justify-between absolute p-4">
                    {/* <!--rating--> */}
                    {item?.average_rating % 1 !== 0 && (
                      <div className="flex items-center space-x-1 bg-white px-2 pt-1 pb-1.5 rounded-full">
                        {renderRatingStars(item?.average_rating || 0)}
                        <p className="text-xs mt-0.5">({item?.average_rating || 0})</p>
                      </div>)}
                    {/* <div>
                      <img className="w-6 h-6" src="assets/icons/heart.svg" alt="rating" />
                    </div> */}
                  </div>
                  {/* <!--profile image--> */}
                  {/* <img src="assets/popular-logo.png" alt="" /> */}
                  {/* 
                      // <div className="w-20 h-16 object-cover overflow-hidden rounded-t-2xl">
                      //   <h2>B</h2>
                      // </div> */}

                  <div className="flex w-full items-end justify-between absolute p-4 -bottom-14">
                    <div className="w-24 rounded border-2 bg-linear-to-r from-[#262626] to-[#1D4B65] text-white bg-[0E2A46] h-16 border-[#EBDABC] flex items-center justify-center">

                      {item.logo == "ssfsfssfsfsfsfs" ?
                        <img
                          src={item.logo || item.name}
                          className="w-full h-16 object-cover overflow-hidden rounded-t-2xl"
                          alt={item.name}
                          loading="lazy"

                        /> :
                        <h2 className="text-4xl font-bold">{item.name.charAt(0)}</h2>

                      }
                    </div>
                    <div className="text-[#fff] flex items-center space-x-2">
                      <img className="w-5 h-5" src="../../assets/icons/white-mug.svg" alt="mug" />
                      {/* <h3 className="text-xl font-medium">�5.00</h3> */}
                      <h3 className="text-lg font-medium">&euro;{item.drinks_start_at}</h3>
                    </div>
                  </div>
                  {/* <img src="assets/popular1.png" className="w-full rounded-t-xl" alt="" /> */}
                  <img src={item?.images?.[0]} alt={item.name}
                    className="w-full h-36  overflow-hidden object-cover rounded-t-xl" />
                </div>
                <div className="px-4 pb-6 pt-14">
                  {/* <h4 className="text-lg text-[#fff] font-medium"><span>Syphax Cafe</span></h4> */}
                  <h4 className="text-md text-white font-medium"><span>{item.name}</span></h4>
                  <div className="mt-2 flex items-center space-x-2">
                    <div className="w-7 h-7 flex-none bg-white rounded-full">
                      <img className="w-full p-2" src="../../assets/icons/location.svg" alt="" />
                    </div>
                    {/* <p className="text-sm text-[#fff]">26 Rue de Ch�teaudun, 75009, Paris</p> */}
                    <p className="text-sm whitespace-nowrap overflow-hidden text-ellipsis text-white">{item.manual_address}</p>
                  </div>
                  <div className="mt-2 flex space-x-2">
                    <div className="w-7 h-7 bg-white flex-none rounded-full">
                      <img className="w-full p-2" src="../../assets/icons/mugs.svg" alt="" />
                    </div>
                    {item.tags?.length > 0 && (
                      <div className="flex items-center gap-2 overflow-hidden">
                        {item.tags?.slice(0, 3).map((tag, idx) => (
                          <span
                            key={idx}
                            className="text-xs px-2 rounded-md py-1 bg-white whitespace-nowrap"
                          >
                            {tag.name}
                          </span>
                        ))}

                        {item.tags.length > 3 && (
                          <span className="text-xs px-2 rounded-md py-1 bg-white whitespace-nowrap">...</span>
                        )}
                      </div>
                    )}

                    {/* <div className="mt-2 flex flex-wrap items-center gap-2">
                      {item.tags?.slice(0, 3).map((tag, idx) => (
                        <span key={idx} className="text-sm px-2 rounded-md py-1 bg-white">
                          {tag.name}
                        </span>
                      ))}
                    </div> */}
                  </div>
                </div>
              </div>

            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    );
  }
}

export default Carousel;
