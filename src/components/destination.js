import React from "react";


const Destination = () => {


    return (
        <section className="bg-destination mt-10 lg:mt-40 xl:h-[700px]">
            <div className="relative">
                <div className=" w-full h-full">
                    <div className="container mx-auto ">
                        <div className="grid xl:grid-cols-2 md:gap-4">
                            <div className="p-10 lg:py-40">
                                <h2 className="text-xl md:text-4xl lg:text-6xl text-white font-base text-center xl:text-left">Simple way to
                                    explore
                                    nightlife destinations
                                </h2>
                                <div className="flex justify-center xl:justify-start gap-2 flex-wrap mt-4 md:mt-8 relative">
                                    <img className="hidden xl:block absolute -right-20 -top-[80%]" src="../../assets/dotted-arrow.svg"
                                        alt="" />
                                    <div className="bg-white p-2 rounded-xl">
                                        <img className="w-20 md:w-28 lg:w-full cursor-pointer" src="../../assets/plastore.svg" alt="" />
                                    </div>
                                    <div className="bg-white p-2 rounded-xl">
                                        <img className="w-20 md:w-28 lg:w-full cursor-pointer" src="../../assets/appstore.svg" alt="" />
                                    </div>
                                </div>
                            </div>
                            <div className="relative">
                                <div className="p-8">
                                    <div className="flex justify-center gap-4">
                                        <div className="xl:-mt-20">
                                            <img className="w-full rounded-2xl border-4 border-white shadow-2xl"
                                                src="../../assets/mobile1.png" alt="" />
                                        </div>
                                        <div className="xl:mt-20">
                                            <img className="w-full rounded-2xl border-4 border-white shadow-2xl"
                                                src="../../assets/mobile2.png" alt="" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {/* <!-- <img src="assets/bg-destinations.svg" alt=""> --> */}
            </div>
        </section>
    );
};

export default Destination;
