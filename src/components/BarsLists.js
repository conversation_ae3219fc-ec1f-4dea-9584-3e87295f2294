import React, { useState } from "react";
import { useRouter } from 'next/router';
import { formatName } from "../hooks/custom"

const BarsLists = ({ bar }) => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [city, setCity] = useState(0);
  const itemsPerPage = 8; // Adjust this based on how many items you want per page
  //const [visibleItems, setVisibleItems] = useState(4); // Initial visible items
  //const city = process.env.NEXT_PUBLIC_CITY;
  // Calculate the total number of pages
  const totalPages = Math.ceil(bar?.length / itemsPerPage);

  // Slice the data based on the current page
  const currentBars = bar?.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  const handlePageChange = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // const handleLoadMore = () => {
  //   setVisibleItems(prev => prev + itemsPerPage); // Increase visible items by `itemsPerPage`
  // };

  const setBarIdInLocalStorage = (id, name) => {
    //const city = process.env.NEXT_PUBLIC_CITY;
    localStorage.setItem("selectedBar", id);
    window.dispatchEvent(new Event("storage"));

    router.push(`/${city}/bar/${name ? formatName(name) : ""}`);
  };




  const getStars = (rating) => {
    //rating = 4.5;
    const full = Math.floor(rating);
    const half = rating % 1 >= 0.5 ? 1 : 0;
    const empty = 5 - full - half;

    return (
      <>
        {Array(full).fill().map((_, i) => (
          <img key={`full-${i}`} className="w-4 h-4" src="../assets/icons/rating.svg" alt="rating" />
        ))}
        {half === 1 && (
          <img className="w-4 h-4" src="../assets/icons/half-rating.svg" alt="half-rating" />
        )}
        {Array(empty).fill().map((_, i) => (
          <img key={`empty-${i}`} className="w-4 h-4" src="../assets/icons/no-rating.svg" alt="no-rating" />
        ))}
      </>
    );
  };


  return (
    <>
      <div className="max-w-6xl mx-auto">
        <div className="relative grid md:grid-cols-2 xl:grid-cols-4 gap-4">
          {currentBars?.map((entry, i) => (
            <div
              key={`bar-${entry.id}-${i}`}
              className="item rounded-2xl bg-gradient-to-r from-[#F3C160] to-[#F77C3E]"
              onClick={() => setBarIdInLocalStorage(entry?.id, entry.name)}

            >
              {/* <div className="rounded-2xl bg-linear-to-r from-[#F3C160] to-[#F77C3E] "> */}
              <div className="relative">
                <div className="flex w-full items-center justify-between absolute p-4">
                  {/* <!--rating--> */}
                  {entry?.rating % 1 !== 0 && (
                    <div className="flex items-center space-x-1 bg-white px-2 pt-1 pb-1.5 rounded-full">

                      {getStars(entry?.rating || 0)}
                      <p className="text-xs mt-0.5">({entry?.rating})</p>

                    </div>)}
                  {/* <div>
                    <img className="w-6 h-6" src="assets/icons/heart.svg" alt="rating" />
                  </div> */}
                </div>
                {/* <!--profile image--> */}
                <div className="flex w-full items-end justify-between absolute p-4 -bottom-16">
                  {entry?.logo == "loadin" ?
                    <div className="w-24 rounded border-2 border-[#EBDABC]">
                      <img src={entry?.logo} alt="" loading="lazy"
                        className="w-full h-20 object-cover overflow-hidden rounded-t-2xl" />
                    </div>
                    :
                    <div className="w-24 rounded border-2 bg-gradient-to-r from-[#F3C160] to-[#F77C3E] text-black bg-[0E2A46] h-16 border-[#EBDABC] flex items-center justify-center">
                      <h2 className="text-4xl font-bold">{entry.name.charAt(0)}</h2>
                    </div>
                  }
                  <div className="text-[#262626] flex items-center space-x-2">
                    <img className="w-5 h-5" src="../../assets/icons/mug.svg" alt="mug" />
                    <h3 className="text-lg font-medium">€{entry.price_start_from}</h3>
                  </div>
                </div>
                <img src={entry?.images[0]?.image}
                  //className="w-full rounded-t-xl" 
                  className="w-full h-36  overflow-hidden object-cover rounded-t-xl"
                  alt="" loading="lazy" />
              </div>
              <div className="px-4 pb-6 pt-14">
                <h4 className="text-md text-[#262626] font-medium"><span>{entry.name}</span></h4>
                <div className="mt-2 flex items-center space-x-2">
                  <div className="w-7 h-7 bg-white flex-none rounded-full">
                    <img className="w-full p-2" src="../../assets/icons/location.svg" alt="" />
                  </div>
                  <p className="text-sm whitespace-nowrap overflow-hidden text-ellipsis">{entry.address}</p>
                </div>
                <div className="mt-2 flex items-center space-x-2">
                  <div className="w-7 h-7 bg-white flex-none rounded-full">
                    <img className="w-full p-2" src="../../assets/icons/mugs.svg" alt="" />
                  </div>

                  <div className="flex items-center gap-2 overflow-hidden">
                    {entry.categories?.slice(0, 3).map((category, catIndex) =>
                      category?.name && category.name !== "" ? (
                        <span
                          key={catIndex}
                          className="text-xs px-2 rounded-md py-1 bg-white whitespace-nowrap"
                        >
                          {category.name}
                        </span>
                      ) : null
                    )}

                    {entry.categories?.length > 3 && (
                      <span className="text-xs px-2 rounded-md py-1 bg-white whitespace-nowrap">...</span>
                    )}
                  </div>

                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="text-center mt-10">
        {/* {visibleItems < bar?.length && (
          <a href="#" className="bg-[#F77C3E] text-white px-4 py-2 rounded-full" onClick={handleLoadMore}>Load More...</a>
      )} */}

        {/* <!--pagintion--> */}
        <div className="flex items-center justify-center mt-10 gap-2">
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="w-10 h-10 p-2 rounded-full 
              border border-[#65807954] text-[#65807954] hover:border-[#658079] 
              hover:text-[#658079] cursor-pointer"
              onClick={() => handlePageChange(currentPage - 1)}
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
            </svg>
          </div>

          <ul className="flex items-center justify-center gap-2">
            {[...Array(totalPages)].map((_, idx) => (
              <li key={idx}>
                <a
                  href="#"
                  className={`w-10 h-10 flex items-center justify-center rounded-full ${currentPage === idx + 1 ? "bg-[#F3C160]" : "bg-[#1D4B65]"
                    } text-white`}
                  onClick={() => handlePageChange(idx + 1)}
                >
                  {idx + 1}
                </a>
              </li>
            ))}
          </ul>

          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="w-10 h-10 p-2 rounded-full border border-[#65807954] text-[#65807954] hover:border-[#658079] hover:text-[#658079] cursor-pointer"
              onClick={() => handlePageChange(currentPage + 1)}
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
            </svg>
          </div>
        </div>
      </div>
    </>

  )
}
export default BarsLists;
