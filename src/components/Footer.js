// components/Footer.js
import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { formatName } from "../hooks/custom"

const Footer = () => {
  const router = useRouter();
  //const city = process.env.NEXT_PUBLIC_CITY;
  const [city, setCity] = useState(0);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const cityFromLocalStorage = localStorage.getItem("city");
      setCity(cityFromLocalStorage || "");
      setIsClient(true);
    }
  }, []);

  const capitalize = (str) => {
    if (!str) return "";
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  const setClubTagInLocalStorage = (tag) => {
    localStorage.setItem("selectedClubTag", tag);
    const tagName = tag != "" ? formatName(tag) : ""
    router.push(`/${city}/clubs/${tagName}`).then(() => {
      router.reload(); // force reload after navigation
    });
  };

  const setTagInLocalStorage = (tag) => {
    localStorage.setItem("selectedTag", tag);
    const tagName = tag != "" ? formatName(tag) : ""
    router.push(`/${city}/bars/${tagName}`).then(() => {
      router.reload(); // force reload after navigation
    });
  };

  const setRedirection = (tag) => {
    router.push(`/${city}/${tag}`);
  };

  return (
    <footer className="bg-[#F3E8D4] mt-20">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 xl:grid-cols-4 gap-10 py-10">
          <div>
            <div className="p-4 bg-white rounded-xl">
              <img className="w-40 mx-auto" src="/assets/logo.svg" alt="Logo" />
              <div className="flex items-center space-x-1 justify-center mt-2">
                <p className="text-xl font-medium">Find your vibe</p>
                <a href="#">
                  <img className="w-6" src="/assets/icons/insta.svg" alt="Instagram" />
                </a>
                <a href="#">
                  <img className="w-6" src="/assets/icons/fb.svg" alt="Facebook" />
                </a>
                <a href="#">
                  <img className="w-6" src="/assets/icons/youtube.svg" alt="YouTube" />
                </a>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="bg-linear-to-r from-[#262626] to-[#1D4B65] p-3 rounded-xl">
                <img className="w-full cursor-pointer" src="/assets/playstore2.svg" alt="Play Store" />
              </div>
              <div className="bg-linear-to-r from-[#262626] to-[#1D4B65] p-3 rounded-xl">
                <img className="w-full cursor-pointer" src="/assets/appstore2.svg" alt="App Store" />
              </div>
            </div>
          </div>
          <div>
            <ul className="space-y-4">
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#"
                  onClick={(e) => {
                    e.preventDefault(); setTagInLocalStorage("Afterwork Bars")
                  }}>
                  Afterwork bars in {capitalize(city)}
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={(e) => {
                  e.preventDefault(); setTagInLocalStorage("Cocktail Bars")
                }}>
                  Cocktail bars in {capitalize(city)}
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={(e) => {
                  e.preventDefault(); setTagInLocalStorage("Happy Hour Bars")
                }}>
                  Happy Hour bars in {capitalize(city)}
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={(e) => {
                  e.preventDefault(); setTagInLocalStorage("Student Bars")
                }}>
                  Student Bars bars in {capitalize(city)}
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={(e) => {
                  e.preventDefault(); setTagInLocalStorage("Sports Bars")
                }}>
                  Sports bars in {capitalize(city)}
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <ul className="space-y-4">
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={(e) => {
                  e.preventDefault(); setClubTagInLocalStorage("Hip-Hop")
                }}>
                  Hip-Hop Clubs in {capitalize(city)}
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={(e) => {
                  e.preventDefault(); setClubTagInLocalStorage("Latino")
                }}>
                  Latino Clubs in {capitalize(city)}
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={(e) => {
                  e.preventDefault(); setClubTagInLocalStorage("Student Clubs")
                }}>
                  Student Clubs in {capitalize(city)}
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={(e) => {
                  e.preventDefault(); setClubTagInLocalStorage("Techno")
                }}>
                  Techno Clubs in {capitalize(city)}
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={(e) => {
                  e.preventDefault(); setClubTagInLocalStorage("VIP Clubs")
                }}>
                  VIP Clubs in {capitalize(city)}
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <ul className="space-y-4">
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={() => setRedirection("about-us")} className="text-left cursor-pointer">
                  About Us
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={() => setRedirection("bars")} className="text-left cursor-pointer">
                  Bars
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={() => setRedirection("clubs")} className="text-left cursor-pointer">
                  Clubs
                </Link>
              </li>
              {/* <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={()=>setRedirection("events")} className="text-left cursor-pointer">
                  Events
                </Link>
              </li> */}
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={() => setRedirection("blogs")} className="text-left cursor-pointer">
                  Blogs
                </Link>
              </li>
              <li className="text-sm text-[#1D4B65] font-medium">
                <Link href="#" onClick={() => setRedirection("contactus")} className="text-left cursor-pointer">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>
          <div className="flex items-center">
            <span className="mr-2">Powered by</span>
            <a href="https://www.srconsulting.com" target="_blank" className="flex items-center space-x-1">
              <img src="/assets/sr-logo.jpg" alt="SR Consulting Logo" className="h-5" />
              <span>SR Consulting</span>
            </a>
          </div>
        </div>
      </div>
      <div className="text-center p-4 bg-[#F77C3E] text-[#fff]">
        © 2025 Seeker.social. All rights reserved.
      </div>
    </footer>
  );
};

export default Footer;
