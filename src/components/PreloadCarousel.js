import React from "react";
//import { useRouter } from 'next/router';

const PreloadCarousel = () => {

  return (
    <section className="mt-10" id="bars-preload">
      <div className="px-4 py-20 animate-pulse">
        <div className="text-center ">
          <h1 className="w-60 mx-auto h-10 rounded bg-gray-100 "></h1>
        </div>
        {/* <!--grid--> */}
        <div className="max-w-6xl mx-auto ">
          <div className="relative grid xl:grid-cols-4 gap-4 mt-10">
            <div className="relative bg-gray-50">
              <div className="flex w-full items-center justify-between absolute p-4">
                <div className="flex items-center space-x-1 px-2 pt-1 pb-1.5 rounded-full">
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                </div>
              </div>
              <div className="flex w-full items-end justify-between absolute p-4 bottom-20">
                <div className="w-24 h-24 bg-gray-200 rounded"></div>
                <div className="text-[#fff] flex items-center space-x-2">
                  <div className="w-5 h-5 bg-gray-200"></div>
                  <div className="w-20 h-5 bg-gray-200"></div>
                </div>
              </div>
              <div className="h-40 bg-gray-100"></div>
              <div className="px-4 pb-6 pt-10"></div>
              <div className=" h-5 bg-gray-100 "></div>
              <div className=" h-5 bg-gray-100 mt-1"></div>
              <div className=" h-5 bg-gray-100 mt-1"></div>
            </div>
            <div className="relative bg-gray-50">
              <div className="flex w-full items-center justify-between absolute p-4">
                <div className="flex items-center space-x-1 px-2 pt-1 pb-1.5 rounded-full">
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                </div>
              </div>
              <div className="flex w-full items-end justify-between absolute p-4 bottom-20">
                <div className="w-24 h-24 bg-gray-200 rounded"></div>
                <div className="text-[#fff] flex items-center space-x-2">
                  <div className="w-5 h-5 bg-gray-200"></div>
                  <div className="w-20 h-5 bg-gray-200"></div>
                </div>
              </div>
              <div className="h-40 bg-gray-100"></div>
              <div className="px-4 pb-6 pt-10"></div>
              <div className=" h-5 bg-gray-100 "></div>
              <div className=" h-5 bg-gray-100 mt-1"></div>
              <div className=" h-5 bg-gray-100 mt-1"></div>
            </div>
            <div className="relative bg-gray-50">
              <div className="flex w-full items-center justify-between absolute p-4">
                <div className="flex items-center space-x-1 px-2 pt-1 pb-1.5 rounded-full">
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                </div>
              </div>
              <div className="flex w-full items-end justify-between absolute p-4 bottom-20">
                <div className="w-24 h-24 bg-gray-200 rounded"></div>
                <div className="text-[#fff] flex items-center space-x-2">
                  <div className="w-5 h-5 bg-gray-200"></div>
                  <div className="w-20 h-5 bg-gray-200"></div>
                </div>
              </div>
              <div className="h-40 bg-gray-100"></div>
              <div className="px-4 pb-6 pt-10"></div>
              <div className=" h-5 bg-gray-100 "></div>
              <div className=" h-5 bg-gray-100 mt-1"></div>
              <div className=" h-5 bg-gray-100 mt-1"></div>
            </div>
            <div className="relative bg-gray-50">
              <div className="flex w-full items-center justify-between absolute p-4">
                <div className="flex items-center space-x-1 px-2 pt-1 pb-1.5 rounded-full">
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                </div>
              </div>
              <div className="flex w-full items-end justify-between absolute p-4 bottom-20">
                <div className="w-24 h-24 bg-gray-200 rounded"></div>
                <div className="text-[#fff] flex items-center space-x-2">
                  <div className="w-5 h-5 bg-gray-200"></div>
                  <div className="w-20 h-5 bg-gray-200"></div>
                </div>
              </div>
              <div className="h-40 bg-gray-100"></div>
              <div className="px-4 pb-6 pt-10"></div>
              <div className=" h-5 bg-gray-100 "></div>
              <div className=" h-5 bg-gray-100 mt-1"></div>
              <div className=" h-5 bg-gray-100 mt-1"></div>
            </div>
          </div>
        </div>
        {/* <BarPopularList data={bar.find(item => item.tag === "Cocktails")?.list || []} /> */}

      </div>
    </section>
  );
};

export default PreloadCarousel;
