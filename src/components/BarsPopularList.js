import React from "react";
import { useRouter } from 'next/router';

const BarPopularList = ({ data }) => {

  const router = useRouter();

  const setBarIdInLocalStorage = (id) => {
    localStorage.setItem("selectedBar", id);
    window.dispatchEvent(new Event("storage"));
    router.push("/bardetails");
  };

  const getStars = (rating) => {
    //rating = 4.5;
    const full = Math.floor(rating);
    const half = rating % 1 >= 0.5 ? 1 : 0;
    const empty = 5 - full - half;

    return (
      <>
        {Array(full).fill().map((_, i) => (
          <img key={`full-${i}`} className="w-4 h-4" src="assets/icons/rating.svg" alt="rating" />
        ))}
        {half === 1 && (
          <img className="w-4 h-4" src="assets/icons/half-rating.svg" alt="half-rating" />
        )}
        {Array(empty).fill().map((_, i) => (
          <img key={`empty-${i}`} className="w-4 h-4" src="assets/icons/no-rating.svg" alt="no-rating" />
        ))}
      </>
    );
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="relative grid xl:grid-cols-3 gap-4 mt-10">
        {/* Left Item */}
        <div className="rounded-2xl grid sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-1 gap-4 cursor-pointer">
          {data.slice(0, 3).map((item, index) => (
            <div key={index}>
              <div className="lg:flex bg-white card-shadow rounded-xl"
                onClick={() => setBarIdInLocalStorage(item?.id)}
              >
                <div className="lg:w-5/12">
                  <img src={item.images[0]?.image} loading="lazy"
                    //  className="w-full rounded-xl" 
                    className="w-full h-full overflow-hidden object-cover rounded-xl"
                    alt={item.name} />
                </div>
                <div className="lg:w-7/12 p-4 space-y-1">
                  <div className="flex w-full items-center justify-between">
                    <div className="flex items-center space-x-1">
                      {/* {Array.isArray(item.ratingIcons) && item.ratingIcons.map((icon, idx) => (
                        <img key={idx} className="w-4 h-4" src={icon} alt="rating" />
                      ))} */}
                      {getStars(item?.rating)}
                      <p className="text-xs mt-0.5">({item?.rating})</p>
                    </div>
                    {/* <div>
                      <img className="w-6 h-6" src="assets/icons/heart.svg" alt="heart" />
                    </div> */}
                  </div>
                  <h4 className="text-md text-[#262626] font-medium">{item.name}</h4>
                  <div className="text-[#262626] flex items-center space-x-2">
                    <img className="w-5 h-5" src="assets/icons/mug.svg" alt="mug" />
                    <h3 className="text-lg font-medium">{item.price_start_from}</h3>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Middle Item */}
        <div className="rounded-2xl bg-[#fff] card-shadow cursor-pointer" onClick={() => setBarIdInLocalStorage(data[3]?.id)}>
          <div className="relative">
            <div className="flex w-full items-center justify-end absolute p-4">
              {/* <div>
                <img className="w-6 h-6" src="assets/icons/heart.svg" alt="heart" />
              </div> */}
            </div>
            <div className="max-h-96 overflow-hidden">
              <img
                src={data[3]?.images[0]?.image || "default-image-url.jpg"}
                className="w-full h-full overflow-hidden object-cover rounded-xl"
                alt={data[3]?.name || "Default Name"}
              />
            </div>
          </div>
          <div className="p-4">
            <div className="flex items-center space-x-1 bg-white">
              {/* {data?.ratingIcons?.map((icon, idx) => (
                <img key={idx} className="w-4 h-4" src={icon} alt="rating" />
              ))} */}
              {getStars(data[3]?.rating || 0)}
              <p className="text-xs mt-0.5">({data[3]?.rating || 0})</p>
            </div>
            <h4 className="text-md text-[#262626] font-medium mt-2">{data[3]?.name || "Default Name"}</h4>
            <div className="text-[#262626] flex items-center space-x-2">
              <img className="w-5 h-5" src="assets/icons/mug.svg" alt="mug" />
              <h3 className="text-lg font-medium">{data[3]?.price_start_from || "0.00"}</h3>
            </div>
            <div className="mt-2 flex items-center space-x-2">
              {/* <p className="text-sm px-2 rounded-md py-1 bg-[#F3C160]">Terrasse</p>
              <p className="text-sm px-2 rounded-md py-1 bg-[#F3C160]">Student Bar</p> */}
              {Array.isArray(data[3]?.categories) &&
                data[3].categories.map((category, idx) => (
                  <p
                    key={idx}
                    className="text-sm px-2 rounded-md py-1 bg-[#F3C160]"
                  >
                    {category.name}
                  </p>
                ))}
            </div>
          </div>
        </div>

        {/* Right Item */}
        <div className="rounded-2xl grid sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-1 gap-4 cursor-pointer">
          {data.slice(4, 7).map((item, index) => (
            <div key={index}>
              <div className="lg:flex bg-white card-shadow rounded-xl"
                onClick={() => setBarIdInLocalStorage(item?.id)}
              >
                <div className="lg:w-5/12">
                  <img src={item?.images[0]?.image} loading="lazy"
                    // className="w-full rounded-xl" 
                    className="w-full h-full overflow-hidden object-cover rounded-xl"
                    alt={item.name} />
                </div>
                <div className="lg:w-7/12 p-4 space-y-1">
                  <div className="flex w-full items-center justify-between">
                    <div className="flex items-center space-x-1">
                      {/* {item.ratingIcons && Array.isArray(item.ratingIcons) && item.ratingIcons.map((icon, idx) => (
                        <img key={idx} className="w-4 h-4" src={icon} alt="rating" />
                      ))} */}
                      {getStars(item?.rating)}
                      <p className="text-xs mt-0.5">({item?.rating})</p>
                    </div>
                    {/* <div>
                      <img className="w-6 h-6" src="assets/icons/heart.svg" alt="heart" />
                    </div> */}
                  </div>
                  <h4 className="text-md text-[#262626] font-medium">{item.name}</h4>
                  <div className="text-[#262626] flex items-center space-x-2">
                    <img className="w-5 h-5" src="assets/icons/mug.svg" alt="mug" />
                    <h3 className="text-lg font-medium">{item.price_start_from}</h3>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BarPopularList;
