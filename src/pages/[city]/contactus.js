import React  from "react";


const contactus = () => {
  
  return (
    <>
     <section className="bg-linear-to-r from-[#F77C3E] via-[#F3C160] to-[#1D4B65]">
  
    <div className="bg-hero-3">
      <div className="text-center container mx-auto px-4 py-28">
        <div>
          <p className="text-lg text-[#fff] font-medium">
            Seeker {">"} <span className="font-bold">Contact Us</span>
          </p>
          <h1 className="text-xl lg:text-4xl font-semibold text-[#F3E8D4]">
            Contact Us
          </h1>
        </div>
      </div>
    </div>
  </section>
  
  <section className="mt-20">
    <div className="max-w-7xl  mx-auto bg-linear-to-r from-[#fbbc98] via-[#f9dbaa] to-[#a4b5b8] bg-opacity-50">
      <div className="px-4 py-10">
        <div className="p-8">
          <h1 className="text-xl lg:text-4xl font-semibold text-[#0E2A46]">
            Get in Touch
          </h1>
          <p className="text-md text-[#333931]">
            Suspendisse ultrice gravida dictum fusce placerat
            ultricies integer
          </p>
          <div className="grid lg:grid-cols-2 gap-10 mt-10">
            <div>
              <div className="bg-[#FFFFFF] p-8 shadow-md rounded-xl space-y-8">
                
                <div className="flex gap-4 ">
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                      className="w-8 text-[#F77C3E] bg-[#ffefec] p-2 rounded-full">
                      <path fill-rule="evenodd"
                        d="m11.54 22.351.07.04.028.016a.76.76 0 0 0 .723 0l.028-.015.071-.041a16.975 16.975 0 0 0 1.144-.742 19.58 19.58 0 0 0 2.683-2.282c1.944-1.99 3.963-4.98 3.963-8.827a8.25 8.25 0 0 0-16.5 0c0 3.846 2.02 6.837 3.963 8.827a19.58 19.58 0 0 0 2.682 2.282 16.975 16.975 0 0 0 1.145.742ZM12 13.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
                        clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-[#4D5756]">
                      Our Address
                    </p>
                    <h4 className="font-bold text-lg">XXXXX XXXXXXXXXXX XXXXX</h4>
                  </div>
                </div>
                
                <div className="flex gap-4 ">
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                      className="w-8 text-[#F77C3E] bg-[#ffefec] p-2 rounded-full">
                      <path fill-rule="evenodd"
                        d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z"
                        clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-[#4D5756]">
                      Our Address
                    </p>
                    <h4 className="font-bold text-lg">Mon - Fri: 9.00am to 5.00pm</h4>
                    <p className="text-sm text-[#4D5756]">
                      [2nd sat Holiday]
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-4 ">
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                      className="w-8 text-[#F77C3E] bg-[#ffefec] p-2 rounded-full">
                      <path fill-rule="evenodd"
                        d="M1.5 4.5a3 3 0 0 1 3-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 0 1-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 0 0 6.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 0 1 1.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 0 1-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5Z"
                        clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm text-[#4D5756]">
                      contact
                    </p>
                    <h4 className="font-bold text-lg">XXXXXXXXXXX</h4>
                    <h4 className="font-bold text-lg"><EMAIL></h4>
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap justify-center items-center lg:justify-between mt-10">
                <div className="flex items-center p-4">
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 p-1">
                      <path fill-rule="evenodd"
                        d="M12 2.25a.75.75 0 0 1 .75.75v16.19l6.22-6.22a.75.75 0 1 1 1.06 1.06l-7.5 7.5a.75.75 0 0 1-1.06 0l-7.5-7.5a.75.75 0 1 1 1.06-1.06l6.22 6.22V3a.75.75 0 0 1 .75-.75Z"
                        clip-rule="evenodd" />
                    </svg>
                  </div>
                  <p>Customer Care</p>
                </div>

                <ul className="flex items-center gap-2">
                  <li>
                    <a href="" className="block w-10 h-10 rounded-full bg-white shadow-md">
                      <img className="w-full p-2" src="../../assets/icons/orange-facebook.jpg" alt="" />
                    </a>
                  </li>
                  <li>
                    <a href="" className="block w-10 h-10 rounded-full bg-white shadow-md">
                      <img className="w-full p-2" src="../../assets/icons/orange-instagram.jpg" alt="" />
                    </a>
                  </li>
                  <li>
                    <a href="" className="block w-10 h-10 rounded-full bg-white shadow-md">
                      <img className="w-full p-2" src="../../assets/icons/orange-pintrest.jpg" alt="" />
                    </a>
                  </li>
                  <li>
                    <a href="" className="block w-10 h-10 rounded-full bg-white shadow-md">
                      <img className="w-full p-2" src="../../assets/icons/orange-twitter.jpg" alt="" />
                    </a>
                  </li>
                </ul>
              </div>
            </div>

            <div>
              <form action="" className="space-y-4">
                <div>
                  <input type="text" name="text" id="" placeholder="Enter Name"
                    className="w-full px-4 py-3 rounded bg-white focus:outline-0 shadow-md text-sm"  required/>
                  <p className="text-red-500 text-xs mt-1 hidden">Please enter name</p>
                </div>
                <div>
                  <input type="email" name="email" id="" placeholder="Enter Mail"
                    className="w-full px-4 py-3 rounded bg-white focus:outline-0 shadow-md text-sm" required />
                  <p className="text-red-500 text-xs mt-1 hidden">Please enter name</p>
                </div>
                <div>
                  <input type="number" name="number" id="" placeholder="Enter Phone Number"
                    className="w-full px-4 py-3 rounded bg-white focus:outline-0 shadow-md text-sm" required />
                  <p className="text-red-500 text-xs mt-1 hidden">Please enter name</p>
                </div>
                <div>
                  <textarea name="" id="" placeholder="summary" rows="5" required
                    className="w-full px-4 py-3 rounded bg-white focus:outline-0 shadow-md text-sm"></textarea>
                </div>
                <div>
                  <button
                    className="text-ms font-medium block px-6 py-3 text-[#fff] uppercase bg-linear-65 from-[#F3C160] to-[#F77C3E] rounded-full text-center">Send
                    Message</button>
                </div>
              </form>
            </div>

          </div>
        </div>
      </div>
    </div>
  </section>

    </>
  )
}
export default contactus;