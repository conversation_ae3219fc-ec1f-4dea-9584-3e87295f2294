import React, { useState, useEffect } from "react";
import { useRouter } from 'next/router';
import Head from 'next/head';
import Accordion from "../../components/Accordion";
import { callApi } from '../../../utils/apiService';
import Carousel from "../../components/Carousel";
//import BarPopularList from "../../components/BarsPopularList";
import Destination from "../../components/destination";
import PreloadCarousel from "../../components/PreloadCarousel";
import { formatName } from "../../hooks/custom"
//import PreloadPopulars from "../../components/PreloadPopulars";


const Bars = () => {

  const router = useRouter();
  const [bar, setBars] = useState([]);
  const [barCount, setBarsCount] = useState([]);
  const [seo, setSEO] = useState([]);
  const [city, setCity] = useState(0);

  // set the selected tag
  const setTagInLocalStorage = (tag) => {
    localStorage.setItem("selectedTag", tag);
    //const city = process.env.NEXT_PUBLIC_CITY;
    var tagname = tag != "" ? formatName(tag) : "";
    router.push(`/${city}/bars/${tagname}`);
  };

  // get the bars details by tags
  useEffect(() => {
    const cityFromLocalStorage = localStorage.getItem("city");
    setCity(cityFromLocalStorage);
    const fetchBarsAndClubs = async () => {
      try {
        const barIdSet = new Set();
        var barlist = [];
        const barsData = await callApi({ method: "GET", url: "api/bars_by_tags_view?page=1&page_size=10" });

        setBars(barsData); // Triggers a re-render
        barsData.forEach(group => {
          group.list.forEach(bar => {
            barIdSet.add(bar.id);
          });
        });
        setBarsCount(barIdSet.size)
      } catch (err) {
        console.error("API Error:", err);
        setError("Failed to fetch bars and clubs.");
      }
    };

    fetchBarsAndClubs();
  }, []);

  //get and set the seo details
  useEffect(() => {
    const fetchSeoDetails = async () => {
      try {
        const [seoData] = await Promise.all([
          callApi({ method: "GET", url: "api/seo" }),
        ]);

        setSEO(seoData);
      } catch (err) {
        console.error("API Error:", err);
      }
    };

    fetchSeoDetails();
  }, []);



  const results = Array.isArray(seo?.results)
    ? seo?.results.filter(item =>
      item?.page_name?.toLowerCase().includes("bars") // "bars" should be lowercase for `.includes()`
    )
    : [];

  // Use the first matched item (if any)
  const filteredPage = results[0];

  // Set the title and meta tags dynamically based on the content
  const pageTitle = filteredPage?.title || "Default Title";
  const pageDescription = filteredPage?.description || "Default description";
  const pageKeywords = filteredPage?.keywords || "bars, paris";
  //const pageURL = "";  // Change this to the dynamic URL of your page


  return (

    <>
      <Head>
        {/* Meta Tags for SEO */}
        {seo.results && (
          <>
            <meta name="title" content={pageTitle} />
            <meta name="description" content={pageDescription} />
            <meta name="keywords" content={pageKeywords} />
            <meta property="og:title" content={pageTitle} />
            <meta property="og:description" content={pageDescription} />
            {/* <meta property="og:image" content="https://example.com/og-image.jpg" /> */}
            {/* <meta property="og:url" content={pageURL} /> */}
            <meta name="twitter:title" content={pageTitle} />
            <meta name="twitter:description" content={pageDescription} />
            {/* <meta name="twitter:image" content="https://example.com/twitter-image.jpg" /> */}
            {/* <meta name="twitter:card" content="summary_large_image" /> */}


            <link rel="icon" href="/favicon.ico" alt="" />
          </>)}
      </Head>

      <section className="bg-linear-to-r from-[#F3C160] to-[#F77C3E]">

        <div className="bg-hero1">
          <div className="text-center container mx-auto px-4 py-20">
            <div>
              <p className="text-lg text-[#fff] font-medium">
                Seeker {`>`} <span className="font-bold">Bars</span>
              </p>
              <h1 className="text-xl lg:text-3xl font-semibold text-[#fff]">
                Best Bars
              </h1>
              <div className="max-w-7xl mx-auto space-y-4 mt-8 text-[#fff]" >
                <p className="text-md text-[##fff] font-medium text-justify">
                  Discover the Best Bars in Paris – Affordable Drinks, Happy Hours, and Unique Experiences
                  Looking for the perfect night out in Paris? Our curated list of bars across the city
                  brings you the best places to enjoy affordable drinks, vibrant happy hours,
                  and unforgettable atmospheres. Whether you are searching for cozy student bars,
                  trendy cocktail lounges, rooftop terraces, hidden speakeasies, or lively karaoke nights,
                  Paris offers a bar scene for every style and budget. Do not pay full price — many of the
                  bars we feature offer generous happy hour deals on beers, wines, and signature cocktails. Explore unique spots in neighborhoods like Oberkampf, Le Marais, and Bastille, and experience Paris nightlife like a local. Find your next favorite bar today and sip your way through the City of Light without breaking the bank!
                </p>
                {/* <p className="text-md text-[##fff] font-medium">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                  industry's standard Lorem Ipsum is simply dummy text of the printing and typesetting industry.Lorem
                  Ipsum is
                  simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                </p> */}
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* <!--the best Bars in...--> */}
      <section>

        <div className="max-w-6xl mx-auto mt-10">
          {/* {bar[0]?.list[0]?.length > 0 && ( */}
          <div className="p-4">
            <div className="grid lg:grid-cols-2 gap-4">
              <div>
                <img className="w-full rounded-xl" loading="lazy" alt=""
                  src={bar?.[0]?.list[0]?.images?.[0]?.image || bar?.[0]?.list[0]?.images?.[0]?.image_url}
                />
              </div>
              <div className="rounded-xl p-4">
                <div>
                  <h1 className="text-xl lg:text-3xl font-semibold text-[#262626]">
                    The Best Bars In...
                  </h1>
                  {/* <!-- <p className="text-lg text-[#F77C3E] font-semibold">Trending This Week</p> --> */}
                </div>
                <p className="text-sm mt-4">{bar?.[0]?.list[0]?.description}
                </p>
                <div className="mt-8 border-t border-[#D9D9D9] grid grid-cols-3">
                  <div className="p-4 border-r border-[#D9D9D9]">
                    <p className="text-sm">Total Bars</p>
                    <h3 className="text-xl lg:text-3xl font-semibold text-[#FFC222]">
                      {barCount ? barCount :0}+
                    </h3>
                  </div>
                  <div className="p-4 border-r border-[#D9D9D9]">
                    <p className="text-sm">Total catagories</p>
                    <h3 className="text-xl lg:text-3xl font-semibold text-[#FFC222]">
                      {
                        bar
                          ?.flatMap(group => group.list || [])
                          ?.flatMap(bar => bar.categories?.map(cat => cat.name) || [])
                          ?.reduce((set, name) => set.add(name), new Set())
                          ?.size || 0
                      }
                    </h3>
                  </div>
                  <div className="p-4 ">
                    <p className="text-sm">Popular</p>
                    <h3 className="text-xl lg:text-3xl font-semibold text-[#FFC222]">
                      {Array.isArray(bar[0]?.list[0]) ? bar[0].list[0].filter(item => item.isPopular).length : 0}+
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* // )} */}
        </div>
      </section>
      {/* <!--Popular Bars--> */}
      {/* <section className="mt-10 bg-linear-to-t from-[#fff] to-[#FFF3DE]">
        <div className="px-4 py-20">
          
          <div className="text-center ">
            
            <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
              Popular Bars
            </h1>
          </div>
          
          {bar?.length > 0 ?
            <>
              <BarPopularList data={bar.find(item => item.tag === "Cocktail Bars")?.list || []} />
            </> : <PreloadPopulars />
          }

        </div>
      </section> */}

      {/* <!--Student Bars--> */}
      {bar.find(item => item.tag === "Student Bars") ?
        <section className="bg-[#fff] bg-linear-to-t from-[#fff] to-[#FFF3DE]">
          <div className="px-4 py-10">
            {/* <!--title--> */}
            <div className="text-center ">

              <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
                Student Bars
              </h1>
            </div>

            {bar?.length > 0 ?
              <>
                <Carousel
                  data={bar.find(item => item.tag === "Student Bars")?.list || []}
                  type="bars"
                />
                <div className="text-center mt-8">
                  <a href="#" className="bg-[#F77C3E] text-white px-4 py-2 rounded-full"
                    onClick={() => {
                      //setSelectedTag("Student Bars");
                      setTagInLocalStorage("Student Bars");
                    }}>View all</a>
                </div></>
              :
              <PreloadCarousel />
            }
          </div>
        </section>
        : ""}

      {/* <!--Affordable ﻿Bars--> */}
      {bar.find(item => item.tag === "Happy Hour Bars") ?
        <section className="bg-[#fff]]">
          <div className="px-4 pt-20">
            {/* <!--title--> */}
            <div className="text-center ">
              {/* <p className="text-lg text-[#F77C3E] font-semibold">Lorem Ipsum is simply</p> */}
              <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
                Affordable Bars
              </h1>
            </div>
            {/* <!--carousel--> */}
            {bar?.length > 0 ?
              <>
                <Carousel
                  data={bar.find(item => item.tag === "Happy Hour Bars")?.list || []}
                  type="bars"
                />
                <div className="text-center mt-8">
                  <a href="#" className="bg-[#F77C3E] text-white px-4 py-2 rounded-full"
                    onClick={() => {
                      //setSelectedTag("Happy Hour Bars");
                      setTagInLocalStorage("Happy Hour Bars");
                    }}
                  >View all</a>
                </div>
              </>
              : <PreloadCarousel />}
          </div>
        </section>
        : ""}
      {/* <!--Afterwork Bars--> */}
      {bar.find(item => item.tag === "Afterwork Bars") ?
        <section>
          <div className="px-4 pt-20">
            {/* <!--title--> */}
            <div className="text-center ">
              {/* <p className="text-lg text-[#F77C3E] font-semibold">Lorem Ipsum is simply</p> */}
              <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
                Afterwork Bars
              </h1>
            </div>
            {/* <!--carousel--> */}
            {bar?.length > 0 ?
              <>
                <Carousel
                  data={bar.find(item => item.tag === "Afterwork Bars")?.list || []}
                  type="bars"
                />
                <div className="text-center mt-8">
                  <a href="#" className="bg-[#F77C3E] text-white px-4 py-2 rounded-full"
                    onClick={() => {
                      //setSelectedTag("Afterwork Bars");
                      setTagInLocalStorage("Afterwork Bars");
                    }}
                  >View all</a>
                </div>
              </>
              : <PreloadCarousel />}
          </div>
        </section>
        : ""}
      {/* <!--destinations--> */}
      <Destination />
      {/* <!--accordions--> */}
      <Accordion type="bars" />
      {/* <section>
    <div className="container mx-auto px-4 py-10 lg:py-20 mt-20">
      <div className="text-center ">
        <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
          Frequently Asked Questions
        </h1>
      </div>
      <div className="w-full space-y-2 mt-10">
        <div className="rounded-lg overflow-hidden">
          <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
            onclick="toggleAccordion(0)">
            <span className="font-medium text-[#658079]">Accordion Item 1</span>
            <svg id="icon-0" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </button>
          <div id="content-0" className="accordion-content open bg-white">This is the content of item 1.</div>
        </div>
        <div className="rounded-lg overflow-hidden">
          <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
            onclick="toggleAccordion(1)">
            <span className="font-medium text-[#658079]">Accordion Item 2</span>
            <svg id="icon-1" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </button>
          <div id="content-1" className="accordion-content bg-white ">This is the content of item 2.</div>
        </div>
        <div className="rounded-lg overflow-hidden">
          <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
            onclick="toggleAccordion(2)">
            <span className="font-medium text-[#658079]">Accordion Item 3</span>
            <svg id="icon-2" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </button>
          <div id="content-2" className="accordion-content bg-white ">This is the content of item 3.</div>
        </div>
      </div>
    </div>
  </section> */}


    </>
  );
};

export default Bars;
