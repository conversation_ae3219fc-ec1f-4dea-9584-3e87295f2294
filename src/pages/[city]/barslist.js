import React, { useState, useEffect } from "react";
import Head from 'next/head';
import Accordion from "../../components/Accordion";
import { callApi } from '../../../utils/apiService';
import BarsLists from "../../components/BarsLists";
import { useRouter } from 'next/router';
import { formatName } from "../../hooks/custom"


const BarsList = () => {

  const router = useRouter();
  const [bar, setBars] = useState([]);
  const [storedTag, setStoredTag] = useState("");
  const [storedDescription, setStoredDescription] = useState("");
  const [seo, setSEO] = useState([]);
  const [city, setCity] = useState(0);

  const barCategories = {
    "Happy Hour Bars": "Discover the top happy hour bars in Paris and enjoy drinks at unbeatable prices. From 3 PM to 2 AM all week at pints starting at 3 Euros, many of the city's trendiest spots offer generous discounts on beers, and cocktails. Whether you're meeting up with coworkers, planning a casual date, meeting friends or starting your weekend early, happy hour is the best way to drink smart in Paris. Explore bars in hotspots like Bastille, République, and Canal Saint-Martin with happy hour menus.",

    "Afterwork Bars": "Unwind after a long day at one of Paris’s best afterwork bars. Ideal for professionals looking to relax, these venues offer the perfect mix of ambiance, music, and drink deals. From sleek lounges to rooftop terraces, afterwork bars in Paris serve as a meeting point for colleagues and friends. Enjoy crafted cocktails, draft beers, and light bites in stylish settings across neighborhoods like Le Marais, Opéra, and Montorgueil. Whether you're into chill music or upbeat social vibes, our curated list of afterwork spots makes it easy to transition from office mode to evening fun.",

    "Sports Bar": "Catch every match and game night at the top sports bars in Paris. These venues are equipped with big screens, lively atmospheres, and a happy hour menu of cold beers and pub-style food that keep fans coming back. Whether you're into football, rugby, basketball, or international tournaments, sports bars in Paris are the place to cheer with fellow fans. Located in areas like Bastille, Pigalle, and Châtelet, these bars create the perfect setting for passionate game days.",

    "Cocktail Bars": "Find unique and the finest cocktail bars in Paris. Whether you love classic recipes or creative blends, Cocktail bars in Paris offer a rich cocktail scene with bars that focus on quality ingredients and artistic presentation. Discover speakeasy-inspired lounges, hotel bars, and modern spots where expert bartenders craft every drink to perfection. From the buzzing nightlife of Oberkampf to the upscale charm of Saint-Germain-des-Prés, there's a cocktail experience waiting for every type of drinker. Browse our list of cocktail bars in Paris and find the perfect setting for your next night out.",

    "Speakeasy": "Uncover the hidden side of Paris nightlife with the city’s best speakeasy bars. These secret venues combine prohibition-style charm with high-end mixology and moody, intimate settings. Often tucked behind unmarked doors, bookshelves, or staircases, speakeasy bars offer a unique experience for those looking to escape the usual crowd. Enjoy expertly crafted cocktails, candlelight ambiance, and music that sets the tone for a memorable evening.",

    "Student Bars": "Looking for the best student bars in Paris where the drinks are cheap and the energy is high? From the Latin Quarter to Pigalle, Paris is packed with budget-friendly spots perfect for student nights out. Enjoy discounted cocktails, affordable pints, and themed parties all week long. These bars are known for their casual vibe, happy hour specials, and a crowd that's always ready to party. Discover great spots to meet friends, dance, and enjoy a lively evening without emptying your wallet."
  };


  // set the selected tag
  const setTagInLocalStorage = (tag) => {
    localStorage.setItem("selectedTag", tag);
    //const city = process.env.NEXT_PUBLIC_CITY;
    const tagName = tag != "" ? formatName(tag) : ""
    setStoredDescription(barCategories[tag])
    router.push(`/${city}/bars/${tagName}`);
  };

  // get the bars details by tags
  useEffect(() => {
    const cityFromLocalStorage = localStorage.getItem("city");
    setCity(cityFromLocalStorage);
    const tag = localStorage.getItem("selectedTag");
    if (tag) {
      setStoredTag(tag);
    }
    setStoredDescription(barCategories[tag])
    const fetchBarsAndClubs = async () => {
      try {
        const barsData = await callApi({ method: "GET", url: "api/bars_by_tags_view" });
        setBars(barsData); // Triggers a re-render
      } catch (err) {
        console.error("API Error:", err);
        setError("Failed to fetch bars and clubs.");
      }
    };

    fetchBarsAndClubs();

  }, []);

  //get and set the seo details
  useEffect(() => {
    //setLoading(true);
    const fetchSeoDetails = async () => {
      try {
        const [seoData] = await Promise.all([
          callApi({ method: "GET", url: "api/seo" }),
        ]);

        setSEO(seoData);
        // setError(null);
      } catch (err) {
        console.error("API Error:", err);
      }
    };

    fetchSeoDetails();
  }, []);



  const results = Array.isArray(seo?.results)
    ? seo?.results.filter(item =>
      item?.page_name?.toLowerCase().includes("bars list") // "bars" should be lowercase for `.includes()`
    )
    : [];

  // Use the first matched item (if any)
  const filteredPage = results[0];


  // Set the title and meta tags dynamically based on the content
  const pageTitle = filteredPage?.title || "Default Title";
  const pageDescription = filteredPage?.description || "Default description";
  const pageKeywords = filteredPage?.keywords || "bars, paris";
  //const pageURL = "";  // Change this to the dynamic URL of your page


  return (

    <>
      <Head>
        {/* Meta Tags for SEO */}
        {seo.results && (
          <>
            <meta name="title" content={pageTitle} />
            <meta name="description" content={pageDescription} />
            <meta name="keywords" content={pageKeywords} />
            <meta property="og:title" content={pageTitle} />
            <meta property="og:description" content={pageDescription} />
            {/* <meta property="og:image" content="https://example.com/og-image.jpg" /> */}
            {/* <meta property="og:url" content={pageURL} /> */}
            <meta name="twitter:title" content={pageTitle} />
            <meta name="twitter:description" content={pageDescription} />
            {/* <meta name="twitter:image" content="https://example.com/twitter-image.jpg" /> */}
            {/* <meta name="twitter:card" content="summary_large_image" /> */}


            <link rel="icon" href="/favicon.ico" />
          </>)}
      </Head>
      <section className="bg-linear-to-r from-[#F3C160] to-[#F77C3E]">

        <div className="bg-hero1">
          <div className="text-center container mx-auto px-4 py-20">
            <div>
              <p className="text-lg text-[#fff] font-medium">
                Seeker {`>`} <span className="font-bold">{storedTag}</span>
              </p>
              <h1 className="text-xl lg:text-3xl font-semibold  text-[#fff]">
                {storedTag}
              </h1>
              <div className="max-w-7xl mx-auto space-y-4 mt-8">
                <p className="text-md text-[#fff] font-medium text-justify ">
                  {storedDescription}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* <!--Student Bars--> */}
      <section className="bg-[#fff]">
        {bar.find(item => item.tag === storedTag) ?
          <div className="px-4 pt-10">
            <div className="text-center py-10">
              <h4 className="text-xl lg:text-2xl font-semibold text-[#1D4B65]">{storedTag} </h4>
            </div>
            {/* <!--grid--> */}

            {bar && (
              <BarsLists bar={bar.find(item => item.tag === storedTag)?.list || []} />
            )}

          </div>
          :
          <div className="text-center py-20 ">
            <h4 className="text-xl lg:text-2xl font-semibold text-[#1D4B65]">{storedTag}</h4>
          </div>


        }
      </section>
      {/* <!--Explore other bars--> */}
      <section>
        <div className="container mx-auto px-4 py-10 lg:py-20">
          <h4 className="text-xl lg:text-2xl font-semibold text-[#1D4B65]">Explore other Bars</h4>
          <div>


            <ul className="mt-10 flex flex-wrap gap-6">
              {[...new Set(bar?.map(item => item.tag?.trim()).filter(Boolean))].map((tag, i) => (
                <li key={i}>
                  <a href="#" className="bg-[#F77C3E] text-white px-4 py-3 rounded-full"
                    onClick={() => {
                      setStoredTag(tag);
                      setTagInLocalStorage(tag);
                    }} >
                    {tag}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      {/* <!--accordions--> */}
      {/* <section>
        <div className="container mx-auto px-4 py-10 lg:py-20">
          <div className="text-center ">
            <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
              Frequently Asked Questions
            </h1>
          </div>
          <div className="w-full space-y-2 mt-10">
            <div className="rounded-lg overflow-hidden">
              <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
               >
                <span className="font-medium text-[#658079]">Accordion Item 1</span>
                <svg id="icon-0" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <div id="content-0" className="accordion-content open bg-white">This is the content of item 1.</div>
            </div>
            <div className="rounded-lg overflow-hidden">
              <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
                >
                <span className="font-medium text-[#658079]">Accordion Item 2</span>
                <svg id="icon-1" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <div id="content-1" className="accordion-content bg-white ">This is the content of item 2.</div>
            </div>
            <div className="rounded-lg overflow-hidden">
              <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
               >
                <span className="font-medium text-[#658079]">Accordion Item 3</span>
                <svg id="icon-2" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <div id="content-2" className="accordion-content bg-white ">This is the content of item 3.</div>
            </div>
          </div>
        </div>
      </section> */}
      <Accordion type="bars" />


    </>
  );

}
export default BarsList;
