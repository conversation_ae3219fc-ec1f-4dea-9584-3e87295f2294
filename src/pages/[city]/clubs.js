import React, { useState, useEffect } from "react";
import Head from 'next/head';
import { useRouter } from 'next/router';
import Accordion from "../../components/Accordion";
import { callApi } from '../../../utils/apiService';
import Carousel from "../../components/Carousel";
//import ClubsPopularList from "../../components/ClubsPopularList";
import Destination from "../../components/destination";
import PreloadCarousel from "../../components/PreloadCarousel";
import { formatName } from "../../hooks/custom"
// import PreloadPopulars from "../../components/PreloadPopulars";

const Clubs = () => {
  const router = useRouter();
  // const [location, setLocation] = useState({ lat: null, lng: null });
  //const [error, setError] = useState(null);
  const [clubs, setClubs] = useState([]);
  //const [loading, setLoading] = useState(true);
  //const [selectedClubTag, setSelectedClubTag] = useState("");
  const [seo, setSEO] = useState([]);
  const [city, setCity] = useState(0);

  const setClubTagInLocalStorage = (tag) => {
    localStorage.setItem("selectedClubTag", tag);
    const cityFromLocalStorage = localStorage.getItem("city");
    const city = cityFromLocalStorage;
    //const city = process.env.NEXT_PUBLIC_CITY;
    const tagName = tag != "" ? formatName(tag) : ""
    router.push(`/${city}/clubs/${tagName}`);
  };

  // get and set the clubs list based on tags
  useEffect(() => {
    const cityFromLocalStorage = localStorage.getItem("city");
    //const city = cityFromLocalStorage;
    setCity(cityFromLocalStorage);
    const fetchClubs = async () => {
      try {
        const [clubData] = await Promise.all([
          callApi({ method: "GET", url: "api/clubs_by_tags_view" }),
        ]);

        setClubs(clubData);
      } catch (err) {
        console.error("API Error:", err);
      }
    };

    fetchClubs();
  }, []);

  // get and set the seo details
  useEffect(() => {
    const fetchSeoDetails = async () => {
      try {
        const [seoData] = await Promise.all([
          callApi({ method: "GET", url: "api/seo" }),
        ]);

        setSEO(seoData);
      } catch (err) {
        console.error("API Error:", err);
      }
    };

    fetchSeoDetails();
  }, []);


  const results = Array.isArray(seo?.results)
    ? seo?.results.filter(item =>
      item?.page_name?.toLowerCase().includes("clubs") // "bars" should be lowercase for `.includes()`
    )
    : [];

  // console.log("results---------",results)

  // Use the first matched item (if any)
  const filteredPage = results[0];

  // Set the title and meta tags dynamically based on the content
  const pageTitle = filteredPage?.title || "Default Title";
  const pageDescription = filteredPage?.description || "Default description";
  const pageKeywords = filteredPage?.keywords || "bars, paris";
  //const pageURL = "";  // Change this to the dynamic URL of your page


  return (

    <>
      <Head>
        {/* Meta Tags for SEO */}
        {seo.results && (
          <>
            <meta name="title" content={pageTitle} />
            <meta name="description" content={pageDescription} />
            <meta name="keywords" content={pageKeywords} />
            <meta property="og:title" content={pageTitle} />
            <meta property="og:description" content={pageDescription} />
            {/* <meta property="og:image" content="https://example.com/og-image.jpg" /> */}
            {/* <meta property="og:url" content={pageURL} /> */}
            <meta name="twitter:title" content={pageTitle} />
            <meta name="twitter:description" content={pageDescription} />
            {/* <meta name="twitter:image" content="https://example.com/twitter-image.jpg" /> */}
            {/* <meta name="twitter:card" content="summary_large_image" /> */}


            <link rel="icon" href="/favicon.ico" />
          </>)}
      </Head>
      <section className="bg-linear-to-r from-[#262626] to-[#1D4B65]">

        <div className="bg-hero1">
          <div className="text-center container mx-auto px-4 py-20">
            <div>
              <p className="text-lg text-[#fff] font-medium">
                Seeker {`>`} <span className="font-bold">Clubs</span>
              </p>
              <h1 className="text-xl lg:text-3xl font-semibold text-[#F3E8D4]">
                Best Clubs
              </h1>
              <div className="max-w-7xl mx-auto space-y-4 mt-8">
                <p className="text-md text-[#F3E8D4] font-medium text-justify">
                  Looking for the ultimate nightlife experience in Paris? Find all the information
                  you need in one place. Explore affordable clubs perfect for students and travelers,
                  check VIP table booking options, see average table costs, and learn about dress codes
                  before you go. Whether you are into techno, hip-hop, pop, or house music, Paris offers a
                  diverse club scene with something for everyone. From trendy nightclubs near Champs-Elysées
                  to underground venues in Oberkampf and Bastille, plan your night with confidence. Skip the
                  guesswork get tips on club entry, opening hours, music styles, and even special student nights.
                  Whether you want to dance till sunrise or enjoy a private VIP experience, discover your
                  perfect Paris club here.
                </p>
                {/* <p className="text-md text-[#F3E8D4] font-medium">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                  industry's standard Lorem Ipsum is simply dummy text of the printing and typesetting industry.Lorem
                  Ipsum is
                  simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                </p> */}
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* <!--the best clubs in...--> */}
      <section>
        <div className="max-w-6xl mx-auto mt-10">
          <div className="p-4">
            <div className="grid lg:grid-cols-2 gap-4">
              <div>
                <img className="w-full rounded-xl"
                  loading="lazy"
                  src={clubs?.[0]?.list?.[0]?.images?.[0] || clubs[0]?.list?.[0]?.images?.[0] ||
                    'assets/best-bar.jpeg'} alt="" />
              </div>
              <div className="rounded-xl p-4">
                <div>
                  <h1 className="text-xl lg:text-3xl font-semibold text-[#262626]">
                    The Best Clubs In...
                  </h1>
                  {/* <!-- <p className="text-lg text-[#F77C3E] font-semibold">Trending This Week</p> --> */}
                </div>
                <p className="text-sm mt-2">{clubs?.[0]?.list?.[0]?.description}</p>
                <div className="mt-8 border-t border-[#D9D9D9] grid grid-cols-3">
                  <div className="p-4 border-r border-[#D9D9D9]">
                    <p className="text-sm">Total Bars</p>
                    <h3 className="text-xl lg:text-3xl font-semibold text-[#FFC222]">

                      {clubs?.length || 0}+
                    </h3>
                  </div>
                  <div className="p-4 border-r border-[#D9D9D9]">
                    <p className="text-sm">Total catagories</p>
                    <h3 className="text-xl lg:text-3xl font-semibold text-[#FFC222]">
                      {
                        clubs
                          ?.flatMap(group => group.list || [])
                          ?.flatMap(club => club.categories || [])
                          ?.reduce((map, cat) => map.set(cat.id, cat), new Map())
                          ?.size || 0
                      }+
                    </h3>
                  </div>
                  <div className="p-4 ">
                    <p className="text-sm">Popular</p>
                    <h3 className="text-xl lg:text-3xl font-semibold text-[#FFC222]">
                      {(clubs?.find(group => group.tag?.toLowerCase() === "popular")?.list?.length) || 0}+
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* <!--Popular clubs--> */}

      {/* <section className="mt-10 bg-linear-to-t from-[#fff] to-[#ECF8FF]">
        <div className="px-4 py-20">
         
          <div className="text-center ">
            <p className="text-lg text-[#F77C3E] font-semibold">Lorem Ipsum&nbsp;is simply</p>
            <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
              Popular Clubs
            </h1>
          </div>
          
          {clubs?.length > 0 ?
            <>
              <ClubsPopularList data={clubs.find(item => item.tag === "popular")?.list || []} />
            </> : <PreloadPopulars />
          }

        </div>
      </section> */}
      {/* <!--Student clubs--> */}
      {clubs.find(item => item.tag === "VIP Clubs") ?
        <section className=" bg-linear-to-t from-[#fff] to-[#ECF8FF]">
          <div className="px-4 py-10">
            {/* <!--title--> */}
            <div className="text-center ">
              {/* <p className="text-lg text-[#F77C3E] font-semibold">Lorem Ipsum is simply</p> */}
              <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
                VIP Clubs
              </h1>
            </div>
            {clubs?.length > 0 ?
              <>
                <Carousel
                  data={clubs.find(item => item.tag === "VIP Clubs")?.list || []}
                  type="clubs"
                />
                {/* <!--carousel--> */}
                <div className="text-center mt-8">
                  <a href="#" className="bg-[#F77C3E] text-white px-4 py-2 rounded-full"
                    onClick={() => {
                      setClubTagInLocalStorage("VIP Clubs");
                    }}>View all</a>
                </div>
              </> : <PreloadCarousel />
            }
          </div>
        </section>
        : ""}
      {/* <!--Affordable ﻿Clubs--> */}
      {clubs.find(item => item.tag === "Student Clubs") ?
        <section className="bg-[#fff]]">
          <div className="px-4 pt-20">
            {/* <!--title--> */}
            <div className="text-center ">
              {/* <p className="text-lg text-[#F77C3E] font-semibold">Lorem Ipsum is simply</p> */}
              <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
                Student Clubs
              </h1>
            </div>
            {/* <!--carousel--> */}
            {clubs?.length > 0 ?
              <>
                <Carousel
                  data={clubs.find(item => item.tag === "Student Clubs")?.list || []}
                  type="clubs"
                />
                <div className="text-center mt-8">
                  <a href="#" className="bg-[#F77C3E] text-white px-4 py-2 rounded-full"
                    onClick={() => {
                      setClubTagInLocalStorage("Student Clubs");
                    }}>View all</a>
                </div>
              </> : <PreloadCarousel />
            }
          </div>
        </section>
        : ""}
      {/* <!--Afterwork Bars--> */}
      {clubs.find(item => item.tag === "Techno") ?
        <section className="">
          <div className="px-4 pt-20">
            {/* <!--title--> */}
            <div className="text-center ">
              {/* <p className="text-lg text-[#F77C3E] font-semibold">Lorem Ipsum is simply</p> */}
              <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
                Affordable Clubs
              </h1>
            </div>
            {clubs?.length > 0 ?
              <>
                <Carousel
                  data={clubs.find(item => item.tag === "Techno")?.list || []}
                  type="clubs"
                />
                <div className="text-center mt-8">
                  <a href="#" className="bg-[#F77C3E] text-white px-4 py-2 rounded-full"
                    onClick={() => {
                      setClubTagInLocalStorage("Techno");
                    }}>View all</a>
                </div>
              </> : <PreloadCarousel />
            }
          </div>
        </section>
        : ""}
      {/* <!--destinations--> */}
      <Destination />

      {/* <!--accordions--> */}
      {/* <section>
        <div className="container mx-auto px-4 py-10 lg:py-20 mt-20">
          <div className="text-center ">
            <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
              Frequently Asked Questions
            </h1>
          </div>
          <div className="w-full space-y-2 mt-10">
            <div className="rounded-lg overflow-hidden">
              <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
                >
                <span className="font-medium text-[#658079]">Accordion Item 1</span>
                <svg id="icon-0" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <div id="content-0" className="accordion-content open bg-white">This is the content of item 1.</div>
            </div>
            <div className="rounded-lg overflow-hidden">
              <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
               >
                <span className="font-medium text-[#658079]">Accordion Item 2</span>
                <svg id="icon-1" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <div id="content-1" className="accordion-content bg-white ">This is the content of item 2.</div>
            </div>
            <div className="rounded-lg overflow-hidden">
              <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
               >
                <span className="font-medium text-[#658079]">Accordion Item 3</span>
                <svg id="icon-2" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <div id="content-2" className="accordion-content bg-white ">This is the content of item 3.</div>
            </div>
          </div>
        </div>
      </section> */}
      <Accordion type="clubs" />


    </>
  );
};

export default Clubs;
