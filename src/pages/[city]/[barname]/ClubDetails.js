import React, { useState, useEffect } from "react";
import Head from 'next/head';
import { useRouter } from 'next/router';
import dynamic from "next/dynamic";
import { callApi } from "../../../../utils/apiService";
import { format, parse } from "date-fns";
const Carousel = dynamic(() => import("../../../components/Carousel"), { ssr: false });
import PreloadCarousel from "../../../components/PreloadCarousel"
import Destination from "../../../components/destination";
import PopupModal from "./PopupModal"


const ClubDetails = () => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const [clubs, setClubs] = useState([]);
  const [selectedClubs, setSelectedClub] = useState(null);
  const [city, setCity] = useState(0);
  // const [loading, setLoading] = useState(true);
  // const [error, setError] = useState(null);
  const [openIndex, setOpenIndex] = useState(0); // Open first category by default
  //const city = process.env.NEXT_PUBLIC_CITY;
  // TO get the user location of the user


  const toggleAccordion = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const getStars = (rating) => {
    //rating = 4.5;
    const full = Math.floor(rating);
    const half = rating % 1 >= 0.5 ? 1 : 0;
    const empty = 5 - full - half;

    return (
      <>
        {Array(full).fill().map((_, i) => (
          <img key={`full-${i}`} className="w-4 h-4" src="../../assets/icons/rating.svg" alt="rating" />
        ))}
        {half === 1 && (
          <img className="w-4 h-4" src="../../assets/icons/half-rating.svg" alt="half-rating" />
        )}
        {Array(empty).fill().map((_, i) => (
          <img key={`empty-${i}`} className="w-4 h-4" src="../../assets/icons/no-rating.svg" alt="no-rating" />
        ))}
      </>
    );
  };

  useEffect(() => {

    const clubIdFromLocalStorage = parseInt(localStorage.getItem("selectedClubId"), 10);
    const cityFromLocalStorage = localStorage.getItem("city");
    setCity(cityFromLocalStorage);

    if (Number.isNaN(clubIdFromLocalStorage) || !clubIdFromLocalStorage) {
      console.log("ClubId is invalid or missing, showing modal...");
      setShowModal(true);
    } else {
      setSelectedClub(clubIdFromLocalStorage);
    }

    const handleStorage = () => {
      const clubId = parseInt(localStorage.getItem("selectedClubId"), 10);
      if (clubId && clubs.length > 0) {
        const selected = clubs.find(b => b.id === clubId);
        if (selected) {
          setSelectedClub(selected);
        }
      }
    };

    const fetchclubsAndClubs = async () => {
      try {
        const clubsData = await callApi({ method: "GET", url: "api/clubdetail" });
        setClubs(clubsData);

        const clubId = parseInt(localStorage.getItem("selectedClubId"), 10);
        //alert(clubId);
        if (clubId && clubsData) {
          const selected = clubsData.find(b => b.id === clubId);
          if (selected) {
            setSelectedClub(selected);
          }
        }
      } catch (err) {
        console.error("API Error:", err);
      }
    };

    fetchclubsAndClubs();

  }, [router.asPath]);

  const pageTitle = selectedClubs?.meta_title || "Default Title";
  const pageDescription = selectedClubs?.meta_description || "Default description";
  const pageKeywords = selectedClubs?.meta_keywords || "bars, paris";
  //const pageURL = "";


  return (

    <>

      <Head>
        {/* Meta Tags for SEO */}
        {selectedClubs && (
          <>
            <meta name="title" content={pageTitle} />
            <meta name="description" content={pageDescription} />
            <meta name="keywords" content={pageKeywords} />
            <meta property="og:title" content={pageTitle} />
            <meta property="og:description" content={pageDescription} />
            {/* <meta property="og:image" content="https://example.com/og-image.jpg" /> */}
            {/* <meta property="og:url" content={pageURL} /> */}
            <meta name="twitter:title" content={pageTitle} />
            <meta name="twitter:description" content={pageDescription} />
            {/* <meta name="twitter:image" content="https://example.com/twitter-image.jpg" /> */}
            {/* <meta name="twitter:card" content="summary_large_image" /> */}


            <link rel="icon" href="/favicon.ico" />
          </>)}
      </Head>

      {/* <!--hero--> */}
      <section className="bg-linear-to-t from-[#1D4B65] to-[#262626]">
        <div className="container mx-auto px-4 py-10">
          <div className="grid md:grid-cols-2 gap-10">
            {/* <!--hero--> */}
            {selectedClubs ? (
              <div>
                {/* <!--product preview--> */}
                <div>
                  <img
                    className="w-full h-60  overflow-hidden object-cover rounded-t-xl"
                    src={selectedClubs.images?.[0] || "../../assets/bar-preview.jpeg"}
                    alt="Main Bar Preview"
                  />
                </div>
                {/* <!--product images--> */}
                <div className="grid grid-cols-4 gap-2 mt-4">
                  {selectedClubs?.images?.slice(0, 4)?.map((img, index) => (
                    <div key={img.id || index}>
                      <img
                        className={`w-full h-25 overflow-hidden object-cover rounded-lg ${img.is_primary ? "border-2 border-[#F77C3E]" : ""}`}
                        src={img}
                        alt={`Bar Image ${index + 1}`}
                      />
                    </div>
                  ))}

                </div>
              </div>
            ) : ""}
            {/* <!--product details--> */}
            <div>
              <div className="space-y-4">
                {/* <!--proct-rating--> */}
                <div className="flex items-center space-x-1 text-gray-300">
                  {getStars(selectedClubs?.rating || 0)}
                  <p className="text-xs mt-0.5">({selectedClubs?.rating})</p>
                  <p className="text-xs mt-0.5 underline">Read reviews</p>
                </div>
                {/* <!--product-title--> */}
                <h1 className="text-xl lg:text-3xl font-semibold text-[#EBDABC]">

                  {selectedClubs?.name}
                </h1>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-white rounded-full">
                      <img className="w-full p-2" src="../../assets/icons/location.svg" alt="" />
                    </div>
                    <p className="text-sm text-[#EBDABC]">{selectedClubs?.manual_address}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-white border-2 border-[#0C4D91] rounded-full flex items-center justify-center">
                      <p className="font-bold text-[#0C4D91] uppercase">M</p>
                    </div>
                    <div className="bg-[#0C913F] rounded-full flex items-center justify-center py-1 px-2">
                      <p className="font-bold text-[#fff] uppercase">{selectedClubs?.metro_line_name}</p>
                    </div>
                    <p className="text-sm font-bold text-[#EBDABC]">{selectedClubs?.nearest_metro_station}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {selectedClubs?.tags?.map((tag, index) => (
                    tag?.name && tag.name !== "" ? (
                      <p
                        key={index}
                        className="text-md font-semibold px-2 rounded-md py-1 bg-[#F3C160] border-2 border-[#F3C160]"
                      >
                        {tag?.name}
                      </p>
                    ) : null
                  ))}
                </div>
                <h4 className="text-xl font-bold text-[#F3C160]">Contact: <span>+{selectedClubs?.contact_number}</span></h4>
                <button
                  className="text-white uppercase bg-gradient-to-r from-[#F77C3E] from-20% via-[#F3C160] via-30% to-[#1D4B65] to-90% px-5 py-3 font-bold rounded-full">Book
                  a table</button>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* <!--description--> */}
      <section >
        <div className="container mx-auto px-4 py-10">
          <p className="text-[#262626]">{selectedClubs?.description}</p>
          <p><a href="" className="block text-center my-10 text-[#1D4B65] underline">Read more</a></p>

          {/* </section>
      <section> */}
          {/* <!--Opening hours:--> */}
          <div className="grid md:grid-cols-2 gap-10">
            <div>
              <h3 className="text-2xl text-[#F77C3E] font-bold mb-4">
                Menu:
              </h3>

              <div className="space-y-2">
                <div className="w-full">
                  {selectedClubs?.categories?.map((category, catIndex) =>
                    category.is_visible ? (
                      <div key={category?.id} className="rounded-lg overflow-hidden mb-4">
                        <button
                          className="w-full flex justify-between items-center p-4 bg-[#EBDABC] hover:bg-gray-100 cursor-pointer"
                          onClick={() => toggleAccordion(catIndex)}
                        >
                          <span className="font-semibold text-[#1D4B65]">{category?.name}</span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth="2"
                            stroke="currentColor"
                            className={`w-5 h-5 text-[#1D4B65] transition-transform ${openIndex === catIndex ? "rotate-180" : "rotate-0"
                              }`}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                          </svg>
                        </button>

                        {openIndex === catIndex && (
                          <div className="bg-white py-2">
                            <table className="w-full">
                              <thead>
                                <tr className="bg-[#262626] text-[#F3C160]">
                                  <td className="p-2">
                                    <img className="h-6" src="../../assets/icons/mug2.svg" alt="" />
                                  </td>
                                  <td className="p-2">Happy Hour</td>
                                  <td className="p-2">Standard</td>
                                </tr>
                              </thead>
                              <tbody>
                                {category?.items?.map((item, itemIndex) => (
                                  <tr key={item?.id || itemIndex} className="text-[#1D4B65] border-b">
                                    <td className="p-2 w-70 font-semibold">{item?.name}</td>
                                    <td className="p-2">
                                      {item?.happy_hour_price
                                        ? `€${parseFloat(item?.happy_hour_price).toFixed(2)}`
                                        : "—"}
                                    </td>
                                    <td className="p-2">
                                      {item?.standard_price
                                        ? `€${parseFloat(item?.standard_price).toFixed(2)}`
                                        : "—"}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        )}
                      </div>
                    ) : null
                  )}
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-2xl text-[#F77C3E] font-bold mb-4">
                Opening Hours:
              </h3>
              <div className="bg-[#FFFAF0] p-4">

                <ul>
                  {selectedClubs?.opening_hours?.map((item, index) => {
                    const openTime = item.opens_at
                      ? format(parse(item?.opens_at, "HH:mm:ss", new Date()), "hh:mm a")
                      : null;

                    const closeTime = item?.closes_at
                      ? format(parse(item?.closes_at, "HH:mm:ss", new Date()), "hh:mm a")
                      : null;

                    return (
                      <li
                        key={index}
                        className="text-[#1D4B65] uppercase text-lg flex items-center justify-between flex-wrap gap-2 py-2 border-b border-[#658079]"
                      >
                        <p>{item.day}</p>
                        <p className="font-bold">
                          {item.is_closed ? "Closed" : `${openTime} - ${closeTime}`}
                        </p>
                      </li>
                    );
                  })}
                </ul>

              </div>
            </div>
            {/* <!--Happy hours:--> */}
            {/* <div> 
          <h3 className="text-2xl text-[#F77C3E] font-bold mb-4">
            Happy Hours:
          </h3>
          <div className="bg-[#FFFAF0] p-4">


            <ul>
              {selectedClubs?.happy_hours?.map((item, index) => {
                const openTime = item.starts_at
                  ? format(parse(item?.starts_at, "HH:mm:ss", new Date()), "hh:mm a")
                  : null;

                const closeTime = item?.ends_at
                  ? format(parse(item?.ends_at, "HH:mm:ss", new Date()), "hh:mm a")
                  : null;

                return (
                  <li
                    key={index}
                    className="text-[#1D4B65] uppercase text-lg flex items-center justify-between flex-wrap gap-2 py-2 border-b border-[#658079]"
                  >
                    <p>{item.day}</p>
                    <p className="font-bold">
                      {item.is_closed ? "Closed" : `${openTime} - ${closeTime}`}
                    </p>
                  </li>
                );
              })}
            </ul>

          </div>
        </div> */}
          </div>
        </div>
        {/* <!--Upcoming Events--> */}
        {/* <div className="md:col-span-2">
                      <h3 className="text-2xl text-[#F77C3E] font-bold mb-4">
                        Upcoming Events
                      </h3>
                      <div className="grid md:grid-cols-2 gap-10">
                        <div className="p-4 grid grid-cols-2 gap-2">
                          <div className="bg-gray-300 rounded h-full"></div>
                          <div className="bg-gray-300 rounded h-full"></div>
                          <div className="bg-gray-300 rounded h-full"></div>
                        </div>
                        <div className="p-4">
                          <img className="w-full" src="assets/bar-ad-space.png" alt="" />
                        </div>
                      </div>
                    </div> */}
        {/* <!--Menu:--> */}

      </section>


      {/* <!--Explore other clubs--> */}
      {/* <section>
        <div className="container mx-auto px-4 py-10">
          <h4 className="text-xl lg:text-2xl font-semibold text-[#F77C3E]">Services offered</h4>
          <div>
            <ul className="mt-10 flex flex-wrap gap-4">
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Terrace</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Restoration</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Takeaway</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#F3274C]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Match broadcasting</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Air conditioning</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">DJ Mix</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Disabled Access & Toilets</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Billiards</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Free Wifi</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Darts</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Pinball</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#F3274C]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Table football</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Concerts / Live Music</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#F3274C]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Dogs allowed</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Board games</a>
              </li>
            </ul>
          </div>
        </div>
      </section> */}

      {/* <section className="relative">
        <div className="bg-gradient-to-r from-[#fff]  h-full absolute z-5 w-28"></div>
        <div className="bg-gradient-to-l from-[#fff]  h-full absolute z-5 w-28 right-0"></div>
        
        <div className="">
          <div className="relative grid md:grid-cols-2 xl:grid-cols-4 gap-4 mt-10 owl-carousel owl-theme">
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
          </div>
        </div>
      </section> */}


      {/* <!--destinations--> */}
      <Destination />

      {/* <!--Explore nearby clubs--> */}
      <section className="bg-[#fff]">
        <div className="px-4 pt-20">
          {/* <!--title--> */}
          <div className="text-center ">
            {/* <!-- <p className="text-lg text-[#F77C3E] font-semibold">Lorem Ipsum is simply</p> --> */}
            <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
              Explore Other clubs
            </h1>
          </div>
          {/* <!--carousel--> */}
          {clubs?.length > 0 ?
            <>
              <Carousel
                data={clubs}
                type="clubs"
                size="4"
              />
            </> : <PreloadCarousel />
          }
        </div>
      </section>
      {/* Popup Modal */}
      <PopupModal
        isOpen={showModal}
        message="Please select a Club to view details."
        onCancel={() => setShowModal(false)}
        onContinue={() => {
          setShowModal(false);
          window.location.href = `/${city}/clubs`; // Navigate to the selection page
        }}
      />

    </>
  );
};

export default ClubDetails;
