import React from "react";
import { useRouter } from 'next/router';
import BarDetails from './BarDetails'
import ClubDetails from './ClubDetails';
import NotFound from "../../NotFound";
import BarsList from "../barslist";
import ClubsList from "../clubslist";

const DynamicRouteHandler = () => {
  const router = useRouter();
  // `city`, `barname`, and `index` are extracted from router.query
  const { city, barname, index } = router.query;

  // If query params are not available (during SSR or initial load), return loading
  if (!city || !barname || !index) {
    return <div>Loading...</div>;
  }

  // The index will be an array since it's a catch-all route (`[...index]`)
  const [slugPart] = index;  // Destructure the first part of the index array
  return (
    <>
      {/*Conditionally render based on `barname` */}
      {barname === 'club' ? (
        <ClubDetails slug={slugPart} />
      ) : barname === 'bar' ? (
        <BarDetails slug={slugPart} />
      ) : barname === 'bars' ? (
        <BarsList slug={slugPart} />
      ) : barname === 'clubs' ? (
        <ClubsList slug={slugPart} />
      )
        : (
          <NotFound />
        )}
    </>
  );
};

export default DynamicRouteHandler;
