import React, { useState, useEffect } from "react";
import { useRouter } from 'next/router';
import Head from 'next/head';
import dynamic from "next/dynamic";
import { callApi } from "../../../../utils/apiService";
import { format, parse } from "date-fns";
const Carousel = dynamic(() => import("../../../components/Carousel"), { ssr: false });
import PreloadCarousel from "../../../components/PreloadCarousel"
import Destination from "../../../components/destination";
import PopupModal from "./PopupModal"

const BarsDetails = () => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const [Bars, setBars] = useState([]);
  const [selectedBar, setSelectedBar] = useState(null);
  //const [loading, setLoading] = useState(true);
  //const [error, setError] = useState(null);
  const [openIndex, setOpenIndex] = useState(0); // Open first category by default
  const [city, setCity] = useState(0);
  // const city = process.env.NEXT_PUBLIC_CITY;


  const toggleAccordion = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const getStars = (rating) => {
    //rating = 4.5;
    const full = Math.floor(rating);
    const half = rating % 1 >= 0.5 ? 1 : 0;
    const empty = 5 - full - half;

    return (
      <>
        {Array(full).fill().map((_, i) => (
          <img key={`full-${i}`} className="w-4 h-4" src="../../assets/icons/rating.svg" alt="rating" />
        ))}
        {half === 1 && (
          <img className="w-4 h-4" src="../../assets/icons/half-rating.svg" alt="half-rating" />
        )}
        {Array(empty).fill().map((_, i) => (
          <img key={`empty-${i}`} className="w-4 h-4" src="../../assets/icons/no-rating.svg" alt="no-rating" />
        ))}
      </>
    );
  };

  useEffect(() => {
    // Check barId when the page loads
    const cityFromLocalStorage = localStorage.getItem("city");
    setCity(cityFromLocalStorage);

    const barIdFromLocalStorage = parseInt(localStorage.getItem("selectedBar"), 10);
    if (Number.isNaN(barIdFromLocalStorage) || !barIdFromLocalStorage) {
      console.log("BarId is invalid or missing, showing modal...");
      setShowModal(true);  // Show modal if barId is missing or invalid
    } else {
      //setSelectedBar(barIdFromLocalStorage);  // Store the valid barId
    }

    window.scrollTo(0, 0);

    const handleStorage = () => {
      const barId = parseInt(localStorage.getItem("selectedBar"), 10);
      if (!barId) {
        setShowModal(true);
      }

      if (barId && Bars.length > 0) {
        const selected = Bars.find(b => b.id === barId);
        if (selected) {
          setSelectedBar(selected);
        }
      }
    };

    const fetchBarsAndClubs = async () => {
      try {
        const barsData = await callApi({ method: "GET", url: "api/bars?page=1&page_size=10" });
        setBars(barsData);

        const barId = parseInt(localStorage.getItem("selectedBar"), 10);

        if (Number.isNaN(barId)) {
          console.log('The barId is NaN');
          setShowModal(true); // Example action to show the modal
        } else if (!barId) {
          console.log('The barId is empty or null');
          setShowModal(true); // Action to show the modal
        } else {
          console.log("");
        }

        if (barId && barsData) {
          const selected = barsData.find(b => b.id === barId);
          if (selected) {
            setSelectedBar(selected);
          }
        }
      } catch (err) {
        console.error("API Error:", err);
      }
    };

    fetchBarsAndClubs();
    window.addEventListener("storage", handleStorage);

    // This function will run on route change
    const handleRouteChange = (url) => {
      // console.log('URL changed to:', url);
      const updateId = parseInt(localStorage.getItem("selectedBar"), 10);
      if (updateId) {
        const selected = Bars.find(b => b.id === updateId);
        if (selected) {
          setSelectedBar(selected);
        }
      }
      window.scrollTo(0, 0); // For example, scroll to the top
    };

    // Listen to route change complete event
    router.events.on('routeChangeComplete', handleRouteChange);

    // Cleanup the event listeners when the component is unmounted
    return () => {
      window.removeEventListener("storage", handleStorage);
      router.events.off('routeChangeComplete', handleRouteChange);
    };

  }, []); // Empty dependency array ensures this runs only once on mount
  //console.log("selectedBar-----",selectedBar);

  const pageTitle = selectedBar?.meta_title || "Default Title";
  const pageDescription = selectedBar?.meta_description || "Default description";
  const pageKeywords = selectedBar?.meta_keywords || "bars, paris";
  //const pageURL = "";  // Change this to the dynamic URL of your page

  //if (!isOpen) return null;



  return (

    <>
      <Head>
        {/* Meta Tags for SEO */}
        {selectedBar && (
          <>
            <meta name="title" content={pageTitle} />
            <meta name="description" content={pageDescription} />
            <meta name="keywords" content={pageKeywords} />
            <meta property="og:title" content={pageTitle} />
            <meta property="og:description" content={pageDescription} />
            {/* <meta property="og:image" content="https://example.com/og-image.jpg" /> */}
            {/* <meta property="og:url" content={pageURL} /> */}
            <meta name="twitter:title" content={pageTitle} />
            <meta name="twitter:description" content={pageDescription} />
            {/* <meta name="twitter:image" content="https://example.com/twitter-image.jpg" /> */}
            {/* <meta name="twitter:card" content="summary_large_image" /> */}
            <link rel="icon" alt="" href="/favicon.ico" />
          </>)}
      </Head>

      {/* <!--hero--> */}
      <section className="bg-linear-to-t from-[#fff] to-[#FFF3DE]">
        <div className="container mx-auto px-4 py-10">
          <div className="grid md:grid-cols-2 gap-10">
            {/* <!--hero--> */}

            {selectedBar ? (
              <div>
                {/* <!--product preview--> */}
                <div>
                  {/* <img className="w-full rounded-lg" src="assets/bar-preview.jpeg" alt="" /> */}
                  <img
                    className="w-full rounded-lg"
                    src={selectedBar.images?.[0]?.image || "../../assets/bar-preview.jpeg"}
                    alt="Main Bar Preview"
                  />
                </div>
                {/* <!--product images--> */}
                <div className="grid grid-cols-4 gap-2 mt-4">
                  {selectedBar?.images?.slice(0, 4)?.map((img, index) => (
                    <div key={img.id || index}>
                      <img
                        className={`w-full rounded-lg ${img.is_primary ? "border-2 border-[#F77C3E]" : ""}`}
                        src={img.image}
                        alt={`Bar Image ${index + 1}`}
                      />
                    </div>
                  ))}

                </div>
              </div>
            ) : ""
            }
            {/* <!--product details--> */}
            <div>
              <div className="space-y-4">
                {/* <!--proct-rating--> */}
                <div className="flex items-center space-x-1">
                  {getStars(selectedBar?.rating || 0)}
                  <p className="text-xs mt-0.5">({selectedBar?.rating})</p>
                  <p className="text-xs mt-0.5 underline">Read reviews</p>
                </div>
                {/* <!--product-title--> */}
                <h1 className="text-xl lg:text-3xl font-semibold text-[#262626]">
                  {selectedBar?.name}
                </h1>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-white rounded-full">
                      <img className="w-full p-2" src="../../assets/icons/location.svg" alt="" />
                    </div>
                    <p className="text-sm">{selectedBar?.address}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-white border-2 border-[#0C4D91] rounded-full flex items-center justify-center">
                      <p className="font-bold text-[#0C4D91] uppercase">M</p>
                    </div>

                    <div
                      className={`rounded-full flex items-center justify-center py-1 px-2 bg-[${selectedBar?.nearest_metro_line}]`}
                    >
                      {/* <div className="bg-{selectedBar?.nearest_metro_line} 
                    rounded-full flex items-center justify-center py-1 px-2"> */}
                      <p className="font-bold text-[#fff] uppercase">{selectedBar?.metro_line_name}</p>
                    </div>
                    <p className="text-sm font-bold">{selectedBar?.nearest_metro_station}</p>
                  </div>
                </div>
                <div className="flex items-center flex-wrap gap-2">

                  {selectedBar?.tags?.map((tag, index) => (
                    tag?.name && tag.name !== "" ? (
                      <p
                        key={index}
                        className="text-md font-semibold px-2 rounded-md py-1 whitespace-nowrap bg-[#F3C160] border-2 border-[#F3C160]"
                      >
                        {tag?.name}
                      </p>
                    ) : null
                  ))}

                  <p className="text-md font-semibold px-2 rounded-md py-1 border-2 border-[#F3C160]">+3</p>
                </div>
                <p className="text-md font-semibold px-2 rounded-md py-1 bg-[#F3C160] border-2 border-[#F3C160]">
                  {selectedBar?.description}</p>
                {/* {selectedBar?.categories && (
                  <p className="text-sm">
                    {selectedBar?.categories
                      .map(category => category?.name?.trim())
                      .filter(Boolean)
                      .join(", ")}
                  </p>
                )} */}
                {/* <p className="text-sm">Pizza, tapas & burgers, plus draft beer & simple cocktails, at a buzzy bar with a street
                  terrace.</p> */}
                <h4 className="text-xl font-bold text-[#1D4B65]">Contact: <span>{selectedBar?.contact_phone}</span></h4>
                <button
                  className="text-white uppercase bg-gradient-to-r from-[#F77C3E] from-20% via-[#F3C160] via-30% to-[#1D4B65] to-90% px-5 py-3 font-bold rounded-full">Book
                  a table</button>
              </div>
            </div>
            {/* <!--Opening hours:--> */}
            <div>
              <h3 className="text-2xl text-[#F77C3E] font-bold mb-4">
                Opening Hours:
              </h3>
              <div className="bg-[#FFFAF0] p-4">


                <ul>
                  {selectedBar?.opening_hours?.map((item, index) => {
                    const openTime = item.opens_at
                      ? format(parse(item?.opens_at, "HH:mm:ss", new Date()), "hh:mm a")
                      : null;

                    const closeTime = item?.closes_at
                      ? format(parse(item?.closes_at, "HH:mm:ss", new Date()), "hh:mm a")
                      : null;

                    return (
                      <li
                        key={index}
                        className="text-[#1D4B65] uppercase text-lg flex items-center justify-between flex-wrap gap-2 py-2 border-b border-[#658079]"
                      >
                        <p>{item.day}</p>
                        <p className="font-bold">
                          {item.is_closed ? "Closed" : `${openTime} - ${closeTime}`}
                        </p>
                      </li>
                    );
                  })}
                </ul>

              </div>
            </div>
            {/* <!--Happy hours:--> */}
            <div>
              <h3 className="text-2xl text-[#F77C3E] font-bold mb-4">
                Happy Hours:
              </h3>
              <div className="bg-[#FFFAF0] p-4">


                <ul>
                  {selectedBar?.happy_hours?.map((item, index) => {
                    const openTime = item.starts_at
                      ? format(parse(item?.starts_at, "HH:mm:ss", new Date()), "hh:mm a")
                      : null;

                    const closeTime = item?.ends_at
                      ? format(parse(item?.ends_at, "HH:mm:ss", new Date()), "hh:mm a")
                      : null;

                    return (
                      <li
                        key={index}
                        className="text-[#1D4B65] uppercase text-lg flex items-center justify-between flex-wrap gap-2 py-2 border-b border-[#658079]"
                      >
                        <p>{item.day}</p>
                        <p className="font-bold">
                          {item.is_closed ? "Closed" : `${openTime} - ${closeTime}`}
                        </p>
                      </li>
                    );
                  })}
                </ul>

              </div>
            </div>
            {/* <!--Upcoming Events--> */}
            {/* <div className="md:col-span-2">
              <h3 className="text-2xl text-[#F77C3E] font-bold mb-4">
                Upcoming Events
              </h3>
              <div className="grid md:grid-cols-2 gap-10">
                <div className="p-4 grid grid-cols-2 gap-2">
                  <div className="bg-gray-300 rounded h-full"></div>
                  <div className="bg-gray-300 rounded h-full"></div>
                  <div className="bg-gray-300 rounded h-full"></div>
                </div>
                <div className="p-4">
                  <img className="w-full" src="assets/bar-ad-space.png" alt="" />
                </div>
              </div>
            </div> */}
            {/* <!--Menu:--> */}
            <div>
              <h3 className="text-2xl text-[#F77C3E] font-bold mb-4">
                Menu:
              </h3>

              <div className="space-y-2">
                <div className="w-full">
                  {selectedBar?.categories?.map((category, catIndex) =>
                    category.is_visible ? (
                      <div key={category?.id} className="rounded-lg overflow-hidden mb-4">
                        <button
                          className="w-full flex justify-between items-center p-4 bg-[#EBDABC] hover:bg-gray-100
                             cursor-pointer"
                          onClick={() => toggleAccordion(catIndex)}
                        >
                          <span className="font-semibold text-[#1D4B65]">{category?.name}</span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth="2"
                            stroke="currentColor"
                            className={`w-5 h-5 text-[#1D4B65] transition-transform ${openIndex === catIndex ? "rotate-180" : "rotate-0"
                              }`}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                          </svg>
                        </button>

                        {openIndex === catIndex && (
                          <div className="bg-white py-2">
                            <table className="w-full">
                              <thead>
                                <tr className="bg-[#262626] text-[#F3C160]">
                                  <td className="p-2">
                                    {/* <img className="h-6" src="../../assets/icons/mug2.svg" alt="" /> */}
                                  </td>
                                  <td className="p-2">Happy Hour</td>
                                  <td className="p-2">Standard</td>
                                </tr>
                              </thead>
                              <tbody>
                                {category?.items?.map((item, itemIndex) => (
                                  <tr key={item?.id || itemIndex} className="text-[#1D4B65] border-b">
                                    <td className="p-2 w-70 font-semibold">{item?.name}</td>
                                    <td className="p-2">
                                      {item?.happy_hour_price
                                        ? `€${parseFloat(item?.happy_hour_price).toFixed(2)}`
                                        : "—"}
                                    </td>
                                    <td className="p-2">
                                      {item?.standard_price
                                        ? `€${parseFloat(item?.standard_price).toFixed(2)}`
                                        : "—"}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        )}
                      </div>
                    ) : null
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* <!--Explore other bars--> */}
      {/* <section>
        <div className="container mx-auto px-4 py-10">
          <h4 className="text-xl lg:text-2xl font-semibold text-[#F77C3E]">Services offered</h4>
          <div>
            <ul className="mt-10 flex flex-wrap gap-4">
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Terrace</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Restoration</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Takeaway</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#F3274C]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Match broadcasting</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Air conditioning</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">DJ Mix</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Disabled Access & Toilets</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Billiards</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Free Wifi</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Darts</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Pinball</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#F3274C]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Table football</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Concerts / Live Music</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#F3274C]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Dogs allowed</a>
              </li>
              <li className="flex items-center justify-center gap-2 bg-[#F3E8D4] text-[#1D4B65] p-2 rounded">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4"
                    stroke="currentColor" className="w-6 h-6 p-1 border-2 rounded text-[#0C913F]">
                    <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                  </svg>
                </span>
                <a href="" className="text-mf font-bold">Board games</a>
              </li>
            </ul>
          </div>
        </div>
      </section> */}

      {/* <!--Aubrac Corner La Defense Arena--> */}
      {/* <section>
        <div className="container mx-auto px-4 py-10">
          <p className="font-semibold">Reviews:</p>
          <h4 className="text-xl lg:text-2xl font-semibold text-[#F77C3E]">{selectedBar?.name}</h4>
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex items-center space-x-1">
              {getStars(selectedBar?.rating || 0)}
              <p className="text-xs mt-0.5">({selectedBar?.rating})</p>
            </div>
            <div>
              <button
                className="text-white bg-gradient-to-r from-[#F77C3E] from-20% via-[#F3C160] via-30% to-[#1D4B65] to-90% px-5 py-3 font-bold rounded-lg">Submit
                Review</button>
            </div>
          </div>
        </div>
      </section> */}


      {/* <section className="relative">
        <div className="bg-gradient-to-r from-[#fff]  h-full absolute z-5 w-28"></div>
        <div className="bg-gradient-to-l from-[#fff]  h-full absolute z-5 w-28 right-0"></div>
        
        <div className="">
          <div className="relative grid md:grid-cols-2 xl:grid-cols-4 gap-4 mt-10 owl-carousel owl-theme">
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
            
            <div className="item rounded-2xl bg-[#F3E8D4] px-4 py-10 space-y-2 text-center ">
              <div className="flex items-center justify-center space-x-1">
                <div>
                  <img className="w-6 h-6" src="assets/icons/rating.svg" alt="rating" />
                </div>
                <p className="text-lg mt-0.5 font-bold">4.5</p>
              </div>
              <p className="text-md">Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
              <div className="flex items-center justify-center gap-2 mt-4">
                <div className="w-8 h-8 rounded-full bg-gray-400"></div>
                <p className="text-sm">User Name</p>
              </div>
            </div>
          </div>
        </div>
      </section> */}


      {/* <!--destinations--> */}
      {/* <section className="bg-destination mt-10 lg:mt-40 xl:h-[700px]">
        <div className="relative">
          <div className=" w-full h-full">

            <div className="container mx-auto ">
              <div className="grid xl:grid-cols-2 md:gap-4">
                <div className="p-10 lg:py-40">
                  <h2 className="text-xl md:text-4xl lg:text-6xl text-white font-base text-center xl:text-left">Simple way to
                    explore
                    nightlife destinations
                  </h2>
                  <div className="flex justify-center xl:justify-start gap-2 flex-wrap mt-4 md:mt-8 relative">
                    <img className="hidden xl:block absolute -right-20 -top-[80%]" src="assets/dotted-arrow.svg" alt="" />
                    <div className="bg-white p-2 rounded-xl">
                      <img className="w-20 md:w-28 lg:w-full" src="assets/plastore.svg" alt="" />
                    </div>
                    <div className="bg-white p-2 rounded-xl">
                      <img className="w-20 md:w-28 lg:w-full" src="assets/appstore.svg" alt="" />
                    </div>
                  </div>
                </div>
                <div className="relative">
                  <div className="p-8">
                    <div className="flex justify-center gap-4">
                      <div className="xl:-mt-20">
                        <img className="w-full rounded-2xl border-4 border-white shadow-2xl" src="assets/mobile1.png" alt="" />
                      </div>
                      <div className="xl:mt-20">
                        <img className="w-full rounded-2xl border-4 border-white shadow-2xl" src="assets/mobile2.png" alt="" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
        </div>
      </section> */}
      <Destination />

      {/* <!--Explore nearby Bars--> */}
      <section className="bg-[#fff]">
        <div className="px-4 pt-20">
          {/* <!--title--> */}
          <div className="text-center ">
            {/* <!-- <p className="text-lg text-[#F77C3E] font-semibold">Lorem Ipsum is simply</p> --> */}
            <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
              Explore Other Bars
            </h1>
          </div>
          {/* <!--carousel--> */}
          {Bars?.length > 0 ?
            <>
              <Carousel
                data={Bars}
                type="bars"
                size="4"
              />
            </> :
            <PreloadCarousel />
          }
        </div>
      </section>
      {/* Popup Modal */}
      <PopupModal
        isOpen={showModal}
        message="Please select a Bar to view details."
        onCancel={() => setShowModal(false)}
        onContinue={() => {
          setShowModal(false);
          window.location.href = `/${city}/bars`; // Navigate to the selection page
        }}
      />

    </>
  );
};

export default BarsDetails;
