import React, { useState, useEffect } from "react";
import Head from 'next/head';
import Accordion from "../../components/Accordion";
import { callApi } from '../../../utils/apiService';
//import Clubs from "../../clubs"; 
import ClubsLists from "../../components/ClubsLists";
import { useRouter } from 'next/router';
import { formatName } from "../../hooks/custom"

const ClubsList = () => {
  const [clubs, setClubs] = useState([]);
  const [storedTag, setStoredTag] = useState("");
  const [storedDescription, setStoredDescription] = useState("");
  const [seo, setSEO] = useState([]);
  const [city, setCity] = useState(0);

  const router = useRouter();
  const clubCategories = {
    "VIP Clubs": "Experience the ultimate Paris nightlife at the most exclusive VIP clubs in the city. Designed for those who love luxury, VIP clubs offer high-end interiors, premium bottle service, private tables, and world-class DJs. Whether you're celebrating a special occasion or just looking for a glamorous night out, these venues guarantee an unforgettable evening. Expect strict dress codes, stylish crowds, and vibrant music in top nightlife districts like Champs-Élysées, Bastille, Chatelet or Saint-Germain. Find your vibe and enjoy a night of elegance, Champagne, and top-tier entertainment in the heart of Paris.",
    "Techno": "Dive into the pulsating beats of the underground with the best techno clubs in Paris. From warehouse-style venues to minimalist industrial spaces, Paris is home to a thriving electronic music scene that attracts top DJs from around the world. Whether you prefer deep techno, acid, or experimental sets, the city offers all-night experiences where music and atmosphere come together. Explore iconic spots in districts like Belleville, Oberkampf, and the outskirts where the party lasts until morning. Perfect for serious music lovers, these clubs let you lose yourself in rhythm and raw energy.",
    "Hip-Hop": "Turn up the volume and hit the dance floor at Paris’s top hip-hop clubs. These venues celebrate urban culture with powerful beats, energetic crowds, and DJs spinning the best in French rap, US hip-hop, and R&B. Whether you're into old-school classics or modern trap, hip-hop clubs in Paris deliver a high-energy vibe every weekend. Expect packed dancefloors, themed nights, and parties that run into the early hours. Found in areas like Châtelet, République, and Bastille, these clubs are the go-to choice for fans of rhythm, style, and street sound.",
    "Latino": "Feel the heat of Latin rhythms at the best Latino clubs in Paris. From reggaeton and salsa to bachata and merengue, these venues bring a unique twist to the Parisian club scene. Perfect for dancing all night, Latino clubs are known for their welcoming vibe, colorful decor, and music that keeps the crowd moving. Join locals and international dancers for nights filled with passion, rhythm, and celebration. Discover popular clubs in Bastille, Strasbourg-Saint-Denis, and the Latin Quarter where the energy never fades and every night feels like a fiesta.",
    "Student Clubs": "Discover the best student clubs in Paris where affordability meets excitement. These venues are popular with university students, Erasmus travelers, and young party goers looking for great music, cheap drinks, and a vibrant social atmosphere. Expect themed events, free entry offers, and chart-topping hits from pop, EDM, and hip-hop playlists. Found near major campuses and in nightlife districts like Grands Boulevards, Chatelet, Bastille and Saint-Michel, student clubs offer the perfect setting for dancing, mingling, and making new memories. Whether it's a weekday or weekend, the student nightlife in Paris is always alive."
  };

  // set the selected club id
  const setClubTagInLocalStorage = (id) => {
    localStorage.setItem("selectedClubId", id);
    //const city = process.env.NEXT_PUBLIC_CITY;
    //router.push(`/${city}/clubs/${id}`);
    const tagName = id != "" ? formatName(id) : ""
    setStoredDescription(clubCategories[id])
    const cityFromLocalStorage = localStorage.getItem("city");
    const city=cityFromLocalStorage;
    router.push(`/${city}/clubs/${tagName}`);


  };

  useEffect(() => {
    const tag = localStorage.getItem("selectedClubTag");
    const cityFromLocalStorage = localStorage.getItem("city");
    const city=cityFromLocalStorage;
    setCity(city)

    if (tag) {
      setStoredTag(tag);
    }
    setStoredDescription(clubCategories[tag])
    const fetchBarsAndClubs = async () => {
      try {
        const clubData = await callApi({ method: "GET", url: "api/clubs_by_tags_view" });
        setClubs(clubData);
      } catch (err) {
        console.error("API Error:", err);
        setError("Failed to fetch bars and clubs.");
      }
    };

    fetchBarsAndClubs();
  }, []);


  // Fetch Seo page details
  useEffect(() => {
    const fetchSeoDetails = async () => {
      try {
        const [seoData] = await Promise.all([
          callApi({ method: "GET", url: "api/seo" }),
        ]);

        setSEO(seoData);
      } catch (err) {
        console.error("API Error:", err);
      }
    };

    fetchSeoDetails();
  }, []);



  const results = Array.isArray(seo?.results)
    ? seo?.results.filter(item =>
      item?.page_name?.toLowerCase().includes("clubs list") // "bars" should be lowercase for `.includes()`
    )
    : [];

  // Use the first matched item (if any)
  const filteredPage = results[0];


  // Set the title and meta tags dynamically based on the content
  const pageTitle = filteredPage?.title || "Default Title";
  const pageDescription = filteredPage?.description || "Default description";
  const pageKeywords = filteredPage?.keywords || "bars, paris";
  //const pageURL = "";  // Change this to the dynamic URL of your page



  return (

    <>
      <Head>
        {/* Meta Tags for SEO */}
        {seo.results && (
          <>
            <meta name="title" content={pageTitle} />
            <meta name="description" content={pageDescription} />
            <meta name="keywords" content={pageKeywords} />
            <meta property="og:title" content={pageTitle} />
            <meta property="og:description" content={pageDescription} />
            {/* <meta property="og:image" content="https://example.com/og-image.jpg" /> */}
            {/* <meta property="og:url" content={pageURL} /> */}
            <meta name="twitter:title" content={pageTitle} />
            <meta name="twitter:description" content={pageDescription} />
            {/* <meta name="twitter:image" content="https://example.com/twitter-image.jpg" /> */}
            {/* <meta name="twitter:card" content="summary_large_image" /> */}


            <link rel="icon" href="/favicon.ico" />
          </>)}
      </Head>
      <section className="bg-linear-to-r from-[#262626] to-[#1D4B65]">
        <div className="bg-hero1">
          <div className="text-center container mx-auto px-4 py-20">
            <div>
              <p className="text-lg text-[#fff] font-medium">
                Seeker {`>`} <span className="font-bold">{storedTag}</span>
              </p>
              <h1 className="text-xl lg:text-3xl font-semibold text-[#F3E8D4]">
                {storedTag}
              </h1>
              <div className="max-w-7xl mx-auto space-y-4 mt-8">
                <p className="text-md text-[#F3E8D4] font-medium text-justify">
                  {storedDescription}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* <!--Houses--> */}
      <section className="bg-[#fff]">
        {clubs.find(item => item.tag === storedTag) ?
          <div className="px-4 pt-10">
            <div className="text-center ">
              <h4 className="text-xl lg:text-2xl font-semibold text-[#1D4B65]">{storedTag} </h4>
            </div>
            {/* <!--grid--> */}
            {clubs && (
              <ClubsLists club={clubs.find(item => item.tag === storedTag)?.list || []} />
            )}
          </div>
          :
          <div className="text-center py-20 ">
            <h4 className="text-xl lg:text-2xl font-semibold text-[#1D4B65]">{storedTag}</h4>
            No Clubs found
          </div>
        }
      </section>
      {/* <!--Explore other bars--> */}
      <section>
        <div className="container mx-auto px-4 py-10 lg:py-20">
          <h4 className="text-xl lg:text-2xl font-semibold text-[#1D4B65]">Explore other Clubs</h4>
          <div>

            <ul className="mt-10 flex flex-wrap gap-4">
              {[...new Set(clubs?.map(item => item.tag?.trim()).filter(Boolean))].map((tag, i) => (
                <li key={i}>
                  <a href="#" className="bg-[#F77C3E] text-white px-4 py-3"
                    onClick={() => {
                      setStoredTag(tag);
                      setClubTagInLocalStorage(tag);
                    }} >
                    {tag}
                  </a>
                </li>
              ))}

            </ul>
          </div>
        </div>
      </section>

      {/* <!--accordions--> */}
      {/* <section>
        <div className="container mx-auto px-4 py-10 lg:py-20">
          <div className="text-center ">
            <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
              Frequently Asked Questions
            </h1>
          </div>
          <div className="w-full space-y-2 mt-10">
            <div className="rounded-lg overflow-hidden">
              <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
                >
                <span className="font-medium text-[#658079]">Accordion Item 1</span>
                <svg id="icon-0" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <div id="content-0" className="accordion-content open bg-white">This is the content of item 1.</div>
            </div>
            <div className="rounded-lg overflow-hidden">
              <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
               >
                <span className="font-medium text-[#658079]">Accordion Item 2</span>
                <svg id="icon-1" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <div id="content-1" className="accordion-content bg-white ">This is the content of item 2.</div>
            </div>
            <div className="rounded-lg overflow-hidden">
              <button className="w-full flex justify-between items-center p-4 bg-[#FFF9EC] hover:bg-gray-100"
                >
                <span className="font-medium text-[#658079]">Accordion Item 3</span>
                <svg id="icon-2" className="w-5 h-5 text-[#F77C3E] transition-transform transform"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </button>
              <div id="content-2" className="accordion-content bg-white ">This is the content of item 3.</div>
            </div>
          </div>
        </div>
      </section> */}
      <Accordion />


    </>
  );
};

export default ClubsList;
