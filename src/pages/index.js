import React, { useState, useEffect } from "react";
import Head from 'next/head';
import { callApi } from '../../utils/apiService';
import Carousel from "../components/Carousel";
import Destination from "../components/destination";
import PreloadCarousel from "../components/PreloadCarousel";
import PopupModalCity from "../components/PopupModalCity";

const Home = () => {

    //const [location, setLocation] = useState({ lat: null, lng: null });
    //const [error, setError] = useState(null);
    const [bars, setBars] = useState([]);
    const [clubs, setClubs] = useState([]);
    //const [loading, setLoading] = useState(true);
    const [seo, setSEO] = useState([]);
    const [isModalOpen, setIsModalOpen] = useState(true);
    const [selectedCity, setSelectedCity] = useState(null);


    useEffect(() => {
        const savedCity = localStorage.getItem("selectedCity");
        if (savedCity) {
            console.log("savedCity-----",savedCity)
            setSelectedCity(JSON.parse(savedCity));
            console.log("selectedCity-----",selectedCity)
            setIsModalOpen(false);
        }
    }, []);

    useEffect(() => {
        if (selectedCity) {
            fetchBars();
            fetchClubs();
            fetchSeoDetails();
        }
    }, [selectedCity]);

    const fetchBars = async () => {
        try {
            // const barsData = await callApi({ method: "GET", url: `api/bars?city=${selectedCity}` });
            const barsData = await callApi({ method: "GET", url: `api/bars?page=1&page_size=10` });
            setBars(barsData);
        } catch (error) {
            console.error("Failed to fetch bars:", error);
        }
    };

    const fetchClubs = async () => {
        try {
            // const clubsData = await callApi({ method: "GET", url: `api/clubdetail?city=${selectedCity}` });
            const clubsData = await callApi({ method: "GET", url: `api/clubdetail?page=1&page_size=10` });
            setClubs(clubsData);
        } catch (error) {
            console.error("Failed to fetch clubs:", error);

        }
    };

    const fetchSeoDetails = async () => {
        try {
            const [seoData] = await Promise.all([
                callApi({ method: "GET", url: "api/seo" }),
            ]);

            setSEO(seoData);
            //setError(null);
        } catch (err) {
            console.error("API Error:", err);
            //setError("Failed to fetch bars and clubs.");
        }

    };

    const scrollToDiv = () => {
        document.getElementById("recent-bars").scrollIntoView({ behavior: "smooth" });
    }

    const handleCitySelect = (city) => {
        localStorage.setItem("selectedCity", JSON.stringify(city));
        setSelectedCity(city);
        setIsModalOpen(false);
    };



    const keywordsToMatch = ["home"];
    const results = Array.isArray(seo?.results)
        ? seo.results.filter(item =>
            keywordsToMatch.some(keyword =>
                item?.page_name?.toLowerCase().includes(keyword)
            )
        )
        : [];

    // Use the first matched item (if any)
    const filteredPage = results[0];

    const pageTitle = filteredPage?.title || "Default Title";
    const pageDescription = filteredPage?.description || "Default description";
    const pageKeywords = filteredPage?.keywords || "bars, paris";
    //const pageURL = "";  // Change this to the dynamic URL of your page
    console.log("selectedCity-----",selectedCity)

    return (
        <>
            <Head>
                {/* Meta Tags for SEO */}
                <meta name="title" content={pageTitle} />
                <meta name="description" content={pageDescription} />
                <meta name="keywords" content={pageKeywords} />
                <meta property="og:title" content={pageTitle} />
                <meta property="og:description" content={pageDescription} />
                {/* <meta property="og:image" content="https://example.com/og-image.jpg" /> */}
                {/* <meta property="og:url" content={pageURL} /> */}
                <meta name="twitter:title" content={pageTitle} />
                <meta name="twitter:description" content={pageDescription} />
                {/* <meta name="twitter:image" content="https://example.com/twitter-image.jpg" /> */}
                {/* <meta name="twitter:card" content="summary_large_image" /> */}

                {/* Favicon */}
                <link rel="icon" href="/favicon.ico" />
            </Head>

            {/* City  Selection modal */}
            <PopupModalCity
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onCitySelect={handleCitySelect}
            />
{console.log("selectedCity---------",selectedCity?.name)}
            {/* Hero Section */}
            <section>
                <div className="bg-hero">
                    <div className="grid xl:grid-cols-2 container mx-auto px-4 py-20 lg:py-40">
                        <div className="animate-fade-slide-in">
                            <h1 className="text-4xl lg:text-6xl text-center xl:text-left font-semibold text-[#fff] lg:leading-18">
                                Discover the Best Nightlife in <span className="uppercase">
                                    {selectedCity? selectedCity.name :"--"}</span>
                            </h1>
                            <p className="text-lg text-[#fff] font-semibold mt-4 text-center xl:text-left">
                                From student bars to VIP clubs, <br />
                                <span className="text-[#F3C160]">find your perfect spot tonight!</span>
                            </p>
                            <div className="mt-8">
                                <div className="w-52 mx-auto xl:mx-0">
                                    <a href="#" className="relative text-center block" onClick={() => scrollToDiv()}>
                                        <p className="absolute w-full text-[#fff] h-full flex items-center justify-center font-bold text-lg uppercase">
                                            Explore Now
                                        </p>
                                        <p className="px-6 py-3 text-[#fff] font-bold text-lg uppercase border-gradient">Explore Now</p>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Popular Bars Section */}
            <section>
                <div className="px-4 py-10 lg:py-20" id="recent-bars">
                    <div className="text-center">
                        <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
                            Recently Added Bars
                        </h1>
                    </div>
                    {bars?.length > 0 ?
                        <Carousel data={bars} type='bars' /> : <PreloadCarousel />}
                </div>
            </section>

            {/* Banner Section */}
            <section className="relative">
                <img className="absolute bottom-0" src="assets/banner-absolute.svg" alt="" />
                <img src="assets/banner.svg" alt="" />
            </section>

            {/* Popular Clubs Section */}
            <section>
                <div className="px-4 py-10 lg:py-20">
                    <div className="text-center">
                        <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
                            Recently Added Clubs
                        </h1>
                    </div>
                    {clubs?.length > 0 ?
                        <Carousel data={clubs} type='clubs' /> : <PreloadCarousel />}
                </div>
            </section>

            {/* Destination Section */}
            <Destination />

            {/* Blogs Section */}
            {/* <section>
                <div className="px-4 py-10 lg:py-20 mt-20">
                    <div className="text-center">
                        <p className="text-lg text-[#F77C3E] font-semibold">Our Blogs</p>
                        <h1 className="text-xl lg:text-3xl text-center font-semibold text-[#262626]">
                            Nightlife Guides & Tips
                        </h1>
                    </div>

                    <div className="container mx-auto">
                        <div className="relative grid md:grid-cols-2 lg:grid-cols-3 gap-4 mt-10">
                         
                            <div className="rounded-2xl bg-[#fff] m-4">
                                <div className="relative">
                                    <img src="assets/student-night-life.svg" className="w-full rounded-t-xl" alt="" />
                                </div>
                                <div className="px-4 py-8">
                                    <div className="flex items-center space-x-4">
                                        <div className="flex items-center space-x-1">
                                            <div className="w-4 h-4 text-[#909090]">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5"
                                                    stroke="currentColor" className="w-full">
                                                    <path strokeLinecap="round" strokeLinejoin="round"
                                                        d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                                                </svg>
                                            </div>
                                            <p className="text-sm text-[#909090]">By admin</p>
                                        </div>
                                        <div className="flex items-center space-x-1">
                                            <div className="w-4 h-4 text-[#909090]">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5"
                                                    stroke="currentColor" className="w-full">
                                                    <path strokeLinecap="round" strokeLinejoin="round"
                                                        d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5" />
                                                </svg>
                                            </div>
                                            <p className="text-sm text-[#909090]">16th Nov 2024</p>
                                        </div>
                                    </div>
                                    <h4 className="text-xl text-[#222222] font-semibold mt-2">
                                        Student Nightlife on a Budget
                                    </h4>
                                    <p className="text-sm mt-2">
                                        Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been...
                                    </p>
                                    <p className="text-sm mt-2 flex items-center space-x-1">
                                        <a href="#" className="font-semibold">Read more</a>
                                        <span>
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5"
                                                stroke="currentColor" className="size-6">
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                                            </svg>
                                        </span>
                                    </p>
                                </div>
                            </div>

                            
                           
                        </div>
                    </div>
                </div>
            </section> */}
        </>
    );
};

export default Home;
