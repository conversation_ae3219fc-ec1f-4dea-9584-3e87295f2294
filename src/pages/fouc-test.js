import React from 'react';
import Head from 'next/head';

const FOUCTest = () => {
  return (
    <>
      <Head>
        <title>FOUC Test Page</title>
        <meta name="description" content="Test page to verify FOUC fixes" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* Header Section */}
        <header className="bg-white shadow-lg">
          <div className="container mx-auto px-4 py-6">
            <h1 className="text-3xl font-bold text-gray-800 text-center">
              FOUC Test Page
            </h1>
            <p className="text-gray-600 text-center mt-2">
              Testing Flash of Unstyled Content fixes
            </p>
          </div>
        </header>

        {/* Main Content */}
        <main className="container mx-auto px-4 py-12">
          {/* Hero Section */}
          <section className="text-center mb-12">
            <div className="bg-white rounded-lg shadow-xl p-8 mb-8">
              <h2 className="text-4xl font-bold text-gray-800 mb-4">
                Styled Content Test
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                If you can see this content properly styled without any flash of unstyled content,
                the FOUC fix is working correctly.
              </p>
              <button className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200">
                Test Button
              </button>
            </div>
          </section>

          {/* Grid Section */}
          <section className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <div key={item} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
                <div className="w-full h-32 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg mb-4"></div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  Card {item}
                </h3>
                <p className="text-gray-600">
                  This is a test card with Tailwind CSS styling. It should appear
                  properly styled without any flash.
                </p>
              </div>
            ))}
          </section>

          {/* Animation Test */}
          <section className="bg-white rounded-lg shadow-xl p-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
              Animation Test
            </h2>
            <div className="flex justify-center space-x-4">
              <div className="w-16 h-16 bg-red-500 rounded-full animate-bounce"></div>
              <div className="w-16 h-16 bg-green-500 rounded-full animate-pulse"></div>
              <div className="w-16 h-16 bg-blue-500 rounded-full animate-spin"></div>
            </div>
            <p className="text-center text-gray-600 mt-6">
              These animations should work smoothly without any styling flash.
            </p>
          </section>
        </main>

        {/* Footer */}
        <footer className="bg-gray-800 text-white py-8 mt-12">
          <div className="container mx-auto px-4 text-center">
            <p>&copy; 2024 FOUC Test. All styles should load properly.</p>
          </div>
        </footer>
      </div>
    </>
  );
};

export default FOUCTest;
