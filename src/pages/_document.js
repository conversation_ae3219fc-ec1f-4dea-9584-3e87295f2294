// pages/_document.js
import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Basic Meta */}
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#000000" />

        {/* Favicon & Icons */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/logo192.png" />

        {/* Default Meta Description (can be overridden per page) */}
      

        {/* Fonts */}
        <link
          href="https://fonts.googleapis.com/css2?family=Montserrat+Alternates:wght@100..900&display=swap"
          rel="stylesheet"
        />

        {/* Owl Carousel Styles */}
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css"
        />
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css"
        />

        {/* ✅ DO NOT use Tailwind via <script>, use PostCSS config or import in _app.js */}
        {/* Removed Tailwind browser script which is not intended for production */}
           {/* <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script> */}

      </Head>
      <body>
        <Main />
        <NextScript />

        {/* jQuery and OwlCarousel Scripts */}
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        {/* <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script> */}

        {/* Inline Script for OwlCarousel and Mobile Menu */}
        <script
  dangerouslySetInnerHTML={{
    __html: `
      document.addEventListener('DOMContentLoaded', function () {
        if (window.jQuery) {
        

          // Mobile menu toggle
          const menuBtn = document.getElementById("menu-btn-mob");
          const menu = document.getElementById("menu-mob");

          if (menuBtn && menu) {
            menuBtn.addEventListener("click", () => {
              menu.classList.toggle("hidden");
            });
          }
        }
      });
    `,
  }}
/>
         {/* <script
          dangerouslySetInnerHTML={{
            __html: `
              document.addEventListener('DOMContentLoaded', function () {
                if (window.jQuery) {
                

                const menuBtn = document.getElementById("menu-btn-mob");
                const menu = document.getElementById("menu-mob");

                if (menuBtn && menu) {
                  menuBtn.addEventListener("click", () => {
                    menu.classList.toggle("hidden");
                  });
                }
              });
            `,
          }}
        />
     */}
      </body>
    </Html>
  );
}
