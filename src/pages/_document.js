// pages/_document.js
import { Html, Head, Main, NextScript } from "next/document";

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Basic Meta */}
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#000000" />

        {/* Favicon & Icons */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/logo192.png" />

        {/* Default Meta Description (can be overridden per page) */}

        {/* Preload critical fonts */}
        <link
          rel="preload"
          href="https://fonts.googleapis.com/css2?family=Montserrat+Alternates:wght@100..900&display=swap"
          as="style"
          onLoad="this.onload=null;this.rel='stylesheet'"
        />
        <noscript>
          <link
            href="https://fonts.googleapis.com/css2?family=Montserrat+Alternates:wght@100..900&display=swap"
            rel="stylesheet"
          />
        </noscript>

        {/* Preload Owl Carousel Styles */}
        <link
          rel="preload"
          href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css"
          as="style"
          onLoad="this.onload=null;this.rel='stylesheet'"
        />
        <link
          rel="preload"
          href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css"
          as="style"
          onLoad="this.onload=null;this.rel='stylesheet'"
        />
        <noscript>
          <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css"
          />
          <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css"
          />
        </noscript>
        {/* Inline critical CSS to prevent FOUC */}
        <style jsx global>{`
          /* Critical CSS to prevent FOUC */
          body {
            font-family: "Montserrat Alternates", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #ffffff;
          }

          /* Hide content until styles are loaded */
          .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 1;
            transition: opacity 0.3s ease-out;
          }

          .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
          }

          /* Basic spinner */
          .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #f77c3e;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        `}</style>
      </Head>
      <body>
        {/* Loading overlay to prevent FOUC */}
        <div id="loading-overlay" className="loading-overlay">
          <div className="spinner"></div>
        </div>

        <Main />
        <NextScript />

        {/* jQuery and OwlCarousel Scripts */}
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        {/* <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script> */}

        {/* Inline Script for OwlCarousel, Mobile Menu, and FOUC Prevention */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
      // Hide loading overlay once page is ready
      function hideLoadingOverlay() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
          overlay.classList.add('hidden');
          setTimeout(() => {
            overlay.style.display = 'none';
          }, 300);
        }
      }

      // Hide overlay when DOM is loaded and styles are applied
      document.addEventListener('DOMContentLoaded', function () {
        // Small delay to ensure styles are applied
        setTimeout(hideLoadingOverlay, 100);

        if (window.jQuery) {
          // Mobile menu toggle
          const menuBtn = document.getElementById("menu-btn-mob");
          const menu = document.getElementById("menu-mob");

          if (menuBtn && menu) {
            menuBtn.addEventListener("click", () => {
              menu.classList.toggle("hidden");
            });
          }
        }
      });

      // Fallback: hide overlay on window load
      window.addEventListener('load', hideLoadingOverlay);
    `,
          }}
        />
        {/* <script
          dangerouslySetInnerHTML={{
            __html: `
              document.addEventListener('DOMContentLoaded', function () {
                if (window.jQuery) {
                

                const menuBtn = document.getElementById("menu-btn-mob");
                const menu = document.getElementById("menu-mob");

                if (menuBtn && menu) {
                  menuBtn.addEventListener("click", () => {
                    menu.classList.toggle("hidden");
                  });
                }
              });
            `,
          }}
        />
     */}
      </body>
    </Html>
  );
}
