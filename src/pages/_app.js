// pages/_app.js

//import '../css/globals.css'
//import 'tailwind.config.js';
import "../css/styles.css";
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Script from 'next/script';

function MyApp({ Component, pageProps }) {
  return (
    <>
      <Script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4" strategy="afterInteractive" />

      <div className="flex flex-col min-h-screen animate-fade-slide-in zoom-80">
        <Navbar />
        <main className="flex-grow">
          <Component {...pageProps} />
        </main>
        <Footer />
      </div>
    </>
  );
}

export default MyApp;
