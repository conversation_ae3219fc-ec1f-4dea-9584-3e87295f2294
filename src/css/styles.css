/* fonts*/

.zoom-80 {
  zoom: 90%;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Montserrat Alternates", sans-serif;
}

.swiper-slide {
  cursor: pointer;
}

:root {
  --swiper-navigation-size: 20px;
  color: #000;
}

.swiper-button-next,
.swiper-button-prev {
  /* background: #869791; */
  background: none;
  color: #000 !important;
  padding: 10px;

}

.swiper-button-prev:after,
.swiper-button-next:after {
  margin-top: -95px;
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  line-height: 1;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}


.bg-hero {
  background-image: url("/assets/hero.svg");
  /* ✅ Absolute path */
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
}


.bg-hero-bars {
  background-image: url("/assets/bars.jpg");
  /* ✅ Absolute path */
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-hero-clubs {
  background-image: url("/assets/banner.svg");
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
}

.swiper-pagination {
  display: none !important;
}


.bg-destination {
  background-image: url("/assets/bg-destinations.svg");
  /* ✅ */
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-mult-gradient {
  background-image: url("/assets/bg-multi-gradient.svg");
  /* ✅ */
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
}


.border-gradient {
  border-radius: 50px;
  border: 4px solid transparent;
  background: linear-gradient(45deg, #F3C160, #F77C3E) border-box;
  mask:
    linear-gradient(#000 0 0) padding-box,
    linear-gradient(#000 0 0);
  mask-composite: exclude;
}

.card-shadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}

/*owl carousel css*/
.owl-nav {
  position: absolute;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  bottom: 50%;
}

.owl-nav .owl-prev,
.owl-next {
  border: 1px solid #000 !important;
  border-radius: 50%;
}

.owl-theme .owl-dots .owl-dot span {
  width: 16px;
  height: 16px;
  margin: 5px 7px;
  background: #ffffff;
  border: 2px solid #F3C160 !important;
  display: block;
  /* -webkit-backface-visibility: visible; */
  transition: opacity .2s ease;
  border-radius: 30px;
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
  background: #F77C3E;
}

.owl-carousel .owl-nav button.owl-next,
.owl-carousel .owl-nav button.owl-prev,
.owl-carousel button.owl-dot {
  background-color: #fff;
  border-radius: 100% !important;
}

.owl-nav .owl-prev,
.owl-next {
  border: 1px solid #65807954 !important;
}

.owl-prev span,
.owl-next span {
  display: none;
  /* Hide the default text */
}

.owl-prev span,
.owl-next span {
  display: block;
  font-size: x-large;

}


.owl-prev {
  background: url("/assets/icons/right-arrow.svg") no-repeat center;
  /* ✅ */
  background-size: contain;
}

.owl-next {
  background: url("/assets/icons/right-arrow.svg") no-repeat center;
  /* ✅ */
  background-size: contain;
}


/*owl carousel css start*/

body {
  font-family: "Montserrat Alternates", sans-serif;
}

.bg-hero {
  background-image: url("/assets/hero.svg");
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-destination {
  background-image: url("/assets/bg-destinations.svg");
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
}

@keyframes myfirst {
  0% {
    background-image: url("/assets/hero.svg");
  }

  100% {
    background-image: url("/assets/hero.svg");
  }

  0% {
    background-image: url("/assets/hero.svg");
  }
}


.bg-mult-gradient {
  background-image: url("/assets/bg-multi-gradient.svg");
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
}

.border-gradient {
  border-radius: 50px;
  border: 4px solid transparent;
  background: linear-gradient(45deg, #F3C160, #F77C3E) border-box;
  mask:
    linear-gradient(#000 0 0) padding-box,
    linear-gradient(#000 0 0);
  mask-composite: exclude;
}

.card-shadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}

/*owl carousel css*/
.owl-nav {
  position: absolute;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  bottom: 50%;
}

.owl-nav .owl-prev,
.owl-next {
  border: 1px solid #000 !important;
  border-radius: 50%;
}

.owl-theme .owl-dots .owl-dot span {
  width: 16px;
  height: 16px;
  margin: 5px 7px;
  background: #ffffff;
  border: 2px solid #F3C160 !important;
  display: block;
  /* -webkit-backface-visibility: visible; */
  transition: opacity .2s ease;
  border-radius: 30px;
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
  background: #F77C3E;
}

.owl-carousel .owl-nav button.owl-next,
.owl-carousel .owl-nav button.owl-prev,
.owl-carousel button.owl-dot {
  background-color: #fff;
  border-radius: 100% !important;
}

.owl-nav .owl-prev,
.owl-next {
  border: 1px solid #65807954 !important;
}

.owl-prev {
  background: url('/assets/icons/right-arrow.svg') no-repeat center;
  background-size: contain;
  width: 40px;
  height: 40px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}

.owl-next {
  background: url('/assets/icons/right-arrow.svg') no-repeat center;
  background-size: contain;
  width: 40px;
  height: 40px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;

}

.owl-prev span,
.owl-next span {
  display: none;
  /* Hide the default text */
}

.owl-prev span,
.owl-next span {
  display: block;
  font-size: x-large;

}

/*owl carousel css end*/
/*accordion css*/
.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.accordion-content.open {
  max-height: 200px;
  padding: 1rem;
}


.card-shadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}

.bg-destination {
  background-image: url('/assets/bg-destinations.svg');
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
}

/*accordion css end*/

.swiper-button-next,
.swiper-button-prev {
  /* background: #869791; */
  background: none;
  color: #fff;
  padding: 10px;
  border-radius: 9999px;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  /* background: #6f7c7d; */
  background: none;
  color: #fff;
  text-decoration: none;
}

/* Animation */
section {
  opacity: 1;
  transform: translateY(40px);
  /* transition: all 0.6s ease-out; */
  animation: fadeSlideIn 0.8s ease-out both;
}

.bg-profile-color {
  background-color: #0E2A46;
}

section.in-view {
  opacity: 1;
  transform: translateY(0);
}

.scroll-section.in-view {
  opacity: 1;
  transform: translateY(0);
}

.scroll-section {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease-out;
  padding: 100px 20px;
  background-color: #f1f1f1;
  margin: 100px 0;
  text-align: center;
}

/* Optional: stagger animations for each section */
/* Define the animation */
@keyframes fadeSlideIn {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}